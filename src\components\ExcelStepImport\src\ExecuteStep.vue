<template>
  <div class="execute-step">
    <!-- 导入进行中 -->
    <div v-if="status === ImportStatus.IMPORTING" class="importing-section">
      <div class="import-status">
        <el-icon class="loading-icon" size="48">
          <Loading />
        </el-icon>
        <h4>正在导入数据...</h4>
      </div>
      
      <div class="progress-section">
        <el-progress
          :percentage="progress.percentage"
          :stroke-width="20"
          :text-inside="true"
          :status="progress.percentage === 100 ? 'success' : undefined"
        />
        <p class="progress-text">
          已处理 {{ progress.current }} / {{ progress.total }} 条记录
        </p>
      </div>
    </div>

    <!-- 导入完成 -->
    <div v-else class="import-result">
      <!-- 成功状态 -->
      <div v-if="status === ImportStatus.SUCCESS" class="success-section">
        <div class="result-icon">
          <el-icon class="success-icon" size="64">
            <CircleCheck />
          </el-icon>
        </div>
        <h3 class="result-title">数据导入完成！</h3>
        
        <!-- 结果统计 -->
        <div class="result-stats">
          <div class="stats-cards">
            <div class="stats-card stats-success">
              <div class="stats-number">{{ importResult.success || 0 }}</div>
              <div class="stats-label">成功导入</div>
            </div>
            <div class="stats-card stats-error" v-if="(importResult.error || 0) > 0">
              <div class="stats-number">{{ importResult.error || 0 }}</div>
              <div class="stats-label">导入失败</div>
            </div>
            <div class="stats-card stats-warning" v-if="(importResult.warning || 0) > 0">
              <div class="stats-number">{{ importResult.warning || 0 }}</div>
              <div class="stats-label">警告记录</div>
            </div>
          </div>
        </div>

        <!-- 失败数据下载 -->
        <div v-if="(importResult.error || 0) > 0" class="failure-download">
          <el-button type="warning" @click="$emit('download-failures')">
            <el-icon><Download /></el-icon>
            下载失败数据
          </el-button>
        </div>

        <!-- 详细信息 -->
        <div v-if="importResult.message" class="result-message">
          <el-alert
            :title="importResult.message"
            type="success"
            :closable="false"
            show-icon
          />
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="status === ImportStatus.ERROR" class="error-section">
        <div class="result-icon">
          <el-icon class="error-icon" size="64">
            <CircleClose />
          </el-icon>
        </div>
        <h3 class="result-title">导入失败</h3>
        
        <div class="error-message">
          <el-alert
            title="导入过程中发生错误，请检查数据格式或联系管理员"
            type="error"
            :closable="false"
            show-icon
          />
        </div>
      </div>
    </div>

    <!-- 操作提示 -->
    <div class="operation-tips">
      <el-alert
        v-if="status === ImportStatus.SUCCESS"
        title="导入完成提示"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <div>
            <p>✅ 数据已成功导入到系统中</p>
            <p v-if="(importResult.error || 0) > 0">
              ⚠️ 部分数据导入失败，请下载失败数据文件查看详情
            </p>
            <p>📊 您可以在相关页面查看导入的数据</p>
          </div>
        </template>
      </el-alert>
      
      <el-alert
        v-else-if="status === ImportStatus.ERROR"
        title="错误处理建议"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <div>
            <p>1. 检查Excel文件格式是否正确</p>
            <p>2. 确认必填字段是否都已填写</p>
            <p>3. 检查数据类型是否匹配</p>
            <p>4. 如问题持续存在，请联系系统管理员</p>
          </div>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Loading, CircleCheck, CircleClose, Download } from '@element-plus/icons-vue'
import type { TExcelImportResultObject } from '@/apis/model'
import type { ImportStatus, ImportProgress } from './types'
import { ImportStatus as ImportStatusEnum } from './types'

interface Props {
  importResult: TExcelImportResultObject
  progress: ImportProgress
  status: ImportStatus
}

interface Emits {
  'download-failures': []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 导入状态枚举
const ImportStatus = ImportStatusEnum
</script>

<style scoped>
.execute-step {
  text-align: center;
  padding: 2rem 0;

  .importing-section {
    .import-status {
      margin-bottom: 2rem;

      .loading-icon {
        color: var(--el-color-primary);
        animation: rotate 2s linear infinite;
        margin-bottom: 1rem;
      }

      h4 {
        color: #495057;
        margin: 0;
      }
    }

    .progress-section {
      max-width: 400px;
      margin: 0 auto;

      .progress-text {
        margin-top: 1rem;
        color: #666;
        font-size: 0.9rem;
      }
    }
  }

  .import-result {
    .result-icon {
      margin-bottom: 1.5rem;

      .success-icon {
        color: var(--el-color-success);
      }

      .error-icon {
        color: var(--el-color-error);
      }
    }

    .result-title {
      margin-bottom: 2rem;
      color: #495057;
    }

    .result-stats {
      margin-bottom: 2rem;

      .stats-cards {
        display: flex;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
      }

      .stats-card {
        background: white;
        border: 1px solid #eee;
        border-radius: 8px;
        padding: 1rem 1.5rem;
        min-width: 120px;

        .stats-number {
          font-size: 1.8rem;
          font-weight: bold;
          margin-bottom: 0.5rem;
        }

        .stats-label {
          font-size: 0.9rem;
          color: #666;
        }

        &.stats-success .stats-number {
          color: var(--el-color-success);
        }

        &.stats-error .stats-number {
          color: var(--el-color-error);
        }

        &.stats-warning .stats-number {
          color: var(--el-color-warning);
        }
      }
    }

    .failure-download {
      margin-bottom: 2rem;
    }

    .result-message {
      max-width: 500px;
      margin: 0 auto 2rem;
    }

    .error-message {
      max-width: 500px;
      margin: 0 auto 2rem;
    }
  }

  .operation-tips {
    max-width: 600px;
    margin: 2rem auto 0;
    text-align: left;

    :deep(.el-alert__content) {
      p {
        margin: 0.5rem 0;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
