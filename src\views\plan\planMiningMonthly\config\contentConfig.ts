export const tableItem: BaseTableItem[] = [
  {
    prop: 'planDate',
    label: '计划月份'
  },
  {
    prop: 'projectDepartmentName',
    label: '项目部'
  },
  {
    prop: 'driftMeter',
    label: '掘进米数'
  },
  {
    prop: 'rawOreVolume',
    label: '原矿量'
  },
  {
    prop: 'supportMeter',
    label: '支护米数'
  },
  {
    prop: 'fillingVolume',
    label: '充填量'
  },
  {
    prop: 'dthMeter',
    label: '潜孔米数'
  },
  {
    prop: 'deepHoleMeter',
    label: '中深孔米数'
  },
  {
    prop: 'oreOutputVolume',
    label: '出矿量'
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },

]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
