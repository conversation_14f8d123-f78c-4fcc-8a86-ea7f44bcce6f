import { request } from '@/utils/hsj/service/index'

// 表格配置接口
export interface TableConfig {
  headers: HeaderColumn[]
  mergeRules: MergeRule[]
}

export interface HeaderColumn {
  label: string
  prop?: string
  children?: HeaderColumn[]
  width?: number
}

export interface MergeRule {
  mergeType: 'row' | 'col'
  column?: string
  columns?: string[]
  relateColumn?: string | null
}

/**
 * 获取表格配置
 * @param code 表格代码
 * @returns 表格配置
 */
export function getTableConfig(code: string): Promise<{ data: TableConfig }> {
  return request({
    url: `/common/table/config/${code}`,
    method: 'get'
  })
}

/**
 * 获取表格数据
 * @param code 表格代码
 * @param params 查询参数
 * @returns 表格数据
 */
export function getTableData(code: string, params?: any): Promise<{ data: any[] }> {
  return request({
    url: `/common/table/data/${code}`,
    method: 'get',
    params
  })
}