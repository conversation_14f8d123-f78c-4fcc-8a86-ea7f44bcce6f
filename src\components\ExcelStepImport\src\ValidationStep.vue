<template>
  <div class="validation-step">
    <!-- 文件信息 -->
    <div class="file-info">
      <div class="file-details">
        <el-icon class="file-icon" size="32">
          <Document />
        </el-icon>
        <div class="file-meta">
          <div class="file-name">{{ fileInfo?.name }}</div>
          <div class="file-size">大小: {{ fileInfo?.sizeText }}</div>
        </div>
      </div>
      <el-button type="warning" @click="$emit('re-upload')">重新上传</el-button>
    </div>

    <!-- 统计信息 -->
    <div class="stats-cards">
      <div class="stats-card">
        <div class="stats-number">{{ validationResult.total || 0 }}</div>
        <div class="stats-label">总记录数</div>
      </div>
      <div class="stats-card stats-success">
        <div class="stats-number">{{ validationResult.success || 0 }}</div>
        <div class="stats-label">正确记录</div>
      </div>
      <div class="stats-card stats-error">
        <div class="stats-number">{{ validationResult.error || 0 }}</div>
        <div class="stats-label">错误记录</div>
      </div>
      <div class="stats-card stats-warning">
        <div class="stats-number">{{ validationResult.warning || 0 }}</div>
        <div class="stats-label">警告记录</div>
      </div>
    </div>

    <!-- 数据预览表格 -->
    <div class="preview-section">
      <h4>
        数据预览（前{{ Math.min(previewData.length, 10) }}行）-
        点击单元格可直接编辑
      </h4>

      <div class="preview-table">
        <el-table
          :data="previewData"
          :height="400"
          border
          stripe
          :row-class-name="getRowClassName"
        >
          <!-- 行号列 -->
          <el-table-column label="行号" width="80" align="center">
            <template #default="{ row }">
              {{ row.row || 0 }}
            </template>
          </el-table-column>

          <!-- 数据列 -->
          <el-table-column
            v-for="field in tableFields"
            :key="field.fieldName"
            :prop="field.fieldName"
            :label="field.displayName"
            :min-width="120"
          >
            <template #default="{ row, $index }">
              <EditableCell
                :key="`${$index}-${field.fieldName}`"
                :value="row.data[field.fieldName]"
                :row-index="$index"
                :field-name="field.fieldName"
                :field-label="field.displayName"
                :errors="getCellErrors(row, field.fieldName)"
                :editable="true"
                :has-options="field.hasOptions"
                :options="getFieldOptions(field)"
                :filterable="true"
                @update="handleCellUpdate"
              />
            </template>
          </el-table-column>

          <!-- 状态列 -->
          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-icon
                v-if="getRowType(row) === 'success'"
                class="status-icon success"
                size="18"
              >
                <Check />
              </el-icon>
              <el-icon
                v-else-if="getRowType(row) === 'error'"
                class="status-icon error"
                size="18"
              >
                <Close />
              </el-icon>
              <el-icon
                v-else-if="getRowType(row) === 'warning'"
                class="status-icon warning"
                size="18"
              >
                <Warning />
              </el-icon>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 错误详情 -->
    <div v-if="hasErrors" class="error-details">
      <h4>错误详情</h4>
      <div class="error-list">
        <div
          v-for="error in errorList"
          :key="error.row"
          class="error-item"
        >
          <div class="error-row">第{{ error.row }}行</div>
          <div class="error-message">{{ error.message }}</div>
        </div>
      </div>
    </div>

    <!-- 提示信息 -->
    <div class="hint-info">
      <el-alert title="提示" type="info" :closable="false" show-icon>
        <template #default>
          点击任意单元格可直接编辑内容。有错误的单元格会显示红色边框和警告图标，鼠标悬停可查看详细错误信息。
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Document, Check, Close, Warning } from '@element-plus/icons-vue'
import { validateRow } from '@/apis/excel-import'
import EditableCell from './EditableCell.vue'
import type {
  TExcelImportResultObject,
  TExcelTemplateInfo,
  TExcelDataInfoObject,
  TMessage,
} from '@/apis/model'
import type { FileInfo, ErrorInfo } from './types'

interface Props {
  validationResult: TExcelImportResultObject
  templateInfo: TExcelTemplateInfo | null
  fileInfo: FileInfo | null
}

interface Emits {
  're-upload': []
  'cell-edit': [data: any]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const previewData = computed(() => {
  return (props.validationResult.dataList || []).slice(0, 10)
})

const tableFields = computed(() => {
  return props.templateInfo?.fields || []
})

const hasErrors = computed(() => {
  // 基于当前实际数据状态判断是否有错误或警告
  if (!props.validationResult.dataList) return false

  return props.validationResult.dataList.some(row =>
    row.messages && row.messages.length > 0
  )
})



const errorList = computed<ErrorInfo[]>(() => {
  const errors: ErrorInfo[] = []

  props.validationResult.dataList?.forEach((item) => {
    if (item.messages && item.messages.length > 0) {
      // 将同一行的所有消息合并为一条
      const rowNumber = item.row || 0 // 使用数据中的实际行号
      const messages = item.messages.map(msg => msg.message).join('；')
      const hasError = item.messages.some(msg => msg.type === 'ERROR')

      errors.push({
        type: hasError ? 'ERROR' : 'WARNING',
        message: messages,
        row: rowNumber,
        column: '', // 不显示字段名
        fieldLabel: '', // 不显示字段标签
      })
    }
  })

  return errors
})

// 方法
const getRowClassName = ({ row }: { row: TExcelDataInfoObject }) => {
  const type = getRowType(row)
  return `row-${type}`
}

const getRowType = (row: TExcelDataInfoObject): string => {
  if (!row.messages || row.messages.length === 0) {
    return 'success'
  }

  const hasError = row.messages.some((msg) => msg.type === 'ERROR')
  return hasError ? 'error' : 'warning'
}

const getCellErrors = (
  row: TExcelDataInfoObject,
  fieldName: string
): TMessage[] => {
  if (!row.messages) return []
  return row.messages.filter((msg) => msg.column === fieldName)
}

const getFieldOptions = (
  field: any
): Array<{ value: string; label: string }> => {
  // 从模板信息的fieldOptions中获取选项
  if (field.hasOptions && props.templateInfo?.fieldOptions) {
    const fieldOptions = props.templateInfo.fieldOptions[field.optionKey]
    if (fieldOptions && Array.isArray(fieldOptions)) {
      return fieldOptions.map((option: any) => ({
        value: option.value,
        label: option.label,
      }))
    }
  }

  return []
}

const handleCellUpdate = async (data: any) => {
  // 更新本地数据
  const { rowIndex, fieldName, value } = data

  // 更新原始验证结果数据
  if (props.validationResult.dataList && props.validationResult.dataList[rowIndex]) {
    if (!props.validationResult.dataList[rowIndex].data) {
      props.validationResult.dataList[rowIndex].data = {}
    }
    props.validationResult.dataList[rowIndex].data[fieldName] = value

    // 调用单行验证API
    try {
      const rowData = props.validationResult.dataList[rowIndex]
      const validatedRow = await validateRow(props.templateInfo?.key || '', rowData)

      // 更新验证结果
      props.validationResult.dataList[rowIndex].messages = validatedRow.messages || []
      props.validationResult.dataList[rowIndex].type = validatedRow.type

      console.log('单行验证完成:', validatedRow)
    } catch (error) {
      console.error('单行验证失败:', error)
      // 验证失败时，可以选择保留原有错误信息或清空
    }
  }

  emit('cell-edit', data)
}
</script>

<style scoped>
.validation-step {
  .file-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .file-details {
    display: flex;
    align-items: center;
  }

  .file-icon {
    color: var(--el-color-success);
    margin-right: 1rem;
  }

  .file-meta {
    .file-name {
      font-weight: bold;
      margin-bottom: 0.25rem;
    }

    .file-size {
      color: #666;
      font-size: 0.9rem;
    }
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .stats-card {
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;

    .stats-number {
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .stats-label {
      font-size: 0.9rem;
      color: #666;
    }

    &.stats-success .stats-number {
      color: var(--el-color-success);
    }

    &.stats-error .stats-number {
      color: var(--el-color-error);
    }

    &.stats-warning .stats-number {
      color: var(--el-color-warning);
    }
  }

  .preview-section {
    margin-bottom: 1.5rem;

    h4 {
      margin-bottom: 1rem;
      color: #495057;
    }
  }

  .preview-table {
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;

    :deep(.el-table) {
      .row-success {
        background-color: #f0f9ff;
      }

      .row-warning {
        background-color: #fffbf0;
      }

      .row-error {
        background-color: #fef2f2;
      }
    }
  }

  .status-icon {
    &.success {
      color: var(--el-color-success);
    }

    &.warning {
      color: var(--el-color-warning);
    }

    &.error {
      color: var(--el-color-error);
    }
  }

  .error-details {
    margin-bottom: 1.5rem;

    h4 {
      margin-bottom: 1rem;
      color: #495057;
    }

    .error-list {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 1rem;
      max-height: 200px;
      overflow-y: auto;
    }

    .error-item {
      padding: 0.5rem 0;
      border-bottom: 1px solid #eee;

      &:last-child {
        border-bottom: none;
      }

      .error-row {
        font-weight: bold;
        color: var(--el-color-error);
        margin-bottom: 0.25rem;
      }

      .error-message {
        color: #666;
        font-size: 0.9rem;
      }
    }
  }

  .hint-info {
    margin-top: 1rem;
  }
}
</style>
