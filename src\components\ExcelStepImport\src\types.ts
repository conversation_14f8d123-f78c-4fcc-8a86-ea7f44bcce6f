import type { TExcelTemplateInfo, TExcelImportResultObject, TExcelDataInfoObject, TMessage } from '@/apis/model'

/**
 * 导入步骤枚举
 */
export enum ImportStep {
  UPLOAD = 1,
  VALIDATE = 2,
  EXECUTE = 3
}

/**
 * 导入状态枚举
 */
export enum ImportStatus {
  IDLE = 'idle',
  UPLOADING = 'uploading',
  VALIDATING = 'validating',
  IMPORTING = 'importing',
  SUCCESS = 'success',
  ERROR = 'error'
}

/**
 * 数据行类型枚举
 */
export enum RowType {
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error'
}

/**
 * 组件Props类型
 */
export interface ExcelStepImportProps {
  /** 是否显示弹窗 */
  visible: boolean
  /** 模板key */
  templateKey: string
  /** 弹窗标题 */
  title?: string
  /** 弹窗宽度 */
  width?: string | number
  /** 是否可以关闭弹窗 */
  closable?: boolean
  /** 是否显示遮罩层 */
  modal?: boolean
  /** 最大文件大小(MB) */
  maxFileSize?: number
  /** 允许的文件类型 */
  acceptTypes?: string[]
  /** 是否自动开始导入 */
  autoImport?: boolean
}

/**
 * 组件Emits类型
 */
export interface ExcelStepImportEmits {
  /** 更新visible状态 */
  'update:visible': [visible: boolean]
  /** 导入成功事件 */
  'success': [result: TExcelImportResultObject]
  /** 导入失败事件 */
  'error': [error: any]
  /** 关闭弹窗事件 */
  'close': []
  /** 取消导入事件 */
  'cancel': []
}

/**
 * 文件信息
 */
export interface FileInfo {
  /** 文件对象 */
  file: File
  /** 文件名 */
  name: string
  /** 文件大小 */
  size: number
  /** 文件大小格式化字符串 */
  sizeText: string
  /** 上传时间 */
  uploadTime: Date
}

/**
 * 验证统计信息
 */
export interface ValidationStats {
  /** 总记录数 */
  total: number
  /** 成功记录数 */
  success: number
  /** 错误记录数 */
  error: number
  /** 警告记录数 */
  warning: number
}

/**
 * 导入进度信息
 */
export interface ImportProgress {
  /** 当前处理数量 */
  current: number
  /** 总数量 */
  total: number
  /** 进度百分比 */
  percentage: number
  /** 状态文本 */
  statusText: string
}

/**
 * 可编辑单元格数据
 */
export interface EditableCell {
  /** 行索引 */
  rowIndex: number
  /** 字段名 */
  fieldName: string
  /** 当前值 */
  value: any
  /** 是否正在编辑 */
  editing: boolean
  /** 原始值 */
  originalValue: any
}

/**
 * 表格列配置
 */
export interface TableColumn {
  /** 字段名 */
  prop: string
  /** 显示标签 */
  label: string
  /** 列宽 */
  width?: number | string
  /** 最小宽度 */
  minWidth?: number | string
  /** 是否可编辑 */
  editable?: boolean
  /** 数据类型 */
  type?: string
}

/**
 * 错误信息扩展
 */
export interface ErrorInfo extends TMessage {
  /** 行号 */
  row?: number
  /** 字段显示名 */
  fieldLabel?: string
}

/**
 * 组件内部状态
 */
export interface ComponentState {
  /** 当前步骤 */
  currentStep: ImportStep
  /** 导入状态 */
  status: ImportStatus
  /** 模板信息 */
  templateInfo: TExcelTemplateInfo | null
  /** 上传的文件信息 */
  fileInfo: FileInfo | null
  /** 验证结果 */
  validationResult: TExcelImportResultObject | null
  /** 导入结果 */
  importResult: TExcelImportResultObject | null
  /** 导入进度 */
  progress: ImportProgress
  /** 当前编辑的单元格 */
  editingCell: EditableCell | null
  /** 表格列配置 */
  tableColumns: TableColumn[]
  /** 是否正在加载 */
  loading: boolean
  /** 错误信息 */
  errorMessage: string
}

/**
 * 步骤配置
 */
export interface StepConfig {
  /** 步骤编号 */
  step: ImportStep
  /** 步骤标题 */
  title: string
  /** 步骤描述 */
  description?: string
  /** 是否完成 */
  completed: boolean
  /** 是否激活 */
  active: boolean
  /** 是否可点击 */
  clickable: boolean
}
