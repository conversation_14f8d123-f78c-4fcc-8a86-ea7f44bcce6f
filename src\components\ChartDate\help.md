请创建一个CommonTable组件，参数是一个code，
使用两个接口，一个接口加载表格设置，包括表头的树结构、行合并、列合并的规则。另一个接口加载数据。
加载数据后，根据表头的树结构渲染表头。根据数据和行合并、列合并规则算出那些单元格需要合并。
先不使用接口，使用示例数据。
示例如下：
1.表格设置
```js
{
  header: [
    {label: 'R1', children: [
      {label: 'R1', props: 'a'},
      {label: 'R1', props: 'b'},
    ]}, // 父级和子级的label一样，合并为一个单元格，但按照子级分成多列
    {label: 'R2', props: 'c'},
    {label: 'R3', children: [
      {label: 'R31', props: 'd'},
      {label: 'R31', props: 'e'},
    ]},
  ],
  mergeRules: [
    // mergeType: 'row'表示[column]列相邻不为空且值相等可以合并，但如果存在[relateColumn]，要求[relateColumn]列不为空也值相等
    // mergeType: 'col'表示同一行，[columns]各列不为空且值相等可以合并
    {mergeType: 'row', column: 'a', relateColumn: null}, 
    {mergeType: 'row', column: 'b', relateColumn: 'a'}, 
    {mergeType: 'row', column: 'c', relateColumn: 'b'}, 
    {mergeType: 'row', column: 'd', relateColumn: 'c'},
    {mergeType: 'col', columns: ['a', 'b']},
    {mergeType: 'col', columns: ['c', 'd']},
  ]
}
```
2.数据
``` js
[
  {a: 'a3', b: 'b3', c: 'c3', d: 'c3', e: 'e3'},
  {a: 'a3', b: 'b3', c: 'c3', d: 'c3', e: 'e4'},
  {a: 'a3', b: 'a5', c: 'c5', d: 'd5', e: 'e5'},
  {a: 'a3', b: 'b6', c: 'c6', d: 'd6', e: 'd6'},
  {a: 'a7', b: 'b6', c: 'c7', d: 'd7', e: 'e7'},
  {a: 'a8', b: 'a8', c: 'c8', d: 'd8', e: 'd8'},
]
```

表头合并完，但未合并数据如下
```html
   <table style="width:260pt"> <!--StartFragment--> 
 <colgroup>
  <col width="69" span="5" style="width:52pt"> 
 </colgroup>
 <tbody>
  <tr height="19"> 
   <td colspan="2" rowspan="2" class="xl65">R1</td> 
   <td rowspan="2" class="xl65">R2</td> 
   <td colspan="2" class="xl65">R3</td> 
  </tr> 
  <tr height="19"> 
   <td class="xl65">R31</td> 
   <td class="xl65">R32</td> 
  </tr> 
  <tr height="22"> 
   <td class="xl66">a3</td> 
   <td class="xl66">b3</td> 
   <td class="xl66">c3</td> 
   <td class="xl66">c3</td> 
   <td class="xl66">e3</td> 
  </tr> 
  <tr height="19"> 
   <td class="xl66">a3</td> 
   <td class="xl66">b3</td> 
   <td class="xl66">c3</td> 
   <td class="xl66">c3</td> 
   <td class="xl66">e4</td> 
  </tr> 
  <tr height="19"> 
   <td class="xl66">a3</td> 
   <td class="xl66">a5</td> 
   <td class="xl66">c5</td> 
   <td class="xl66">d5</td> 
   <td class="xl66">e5</td> 
  </tr> 
  <tr height="19"> 
   <td class="xl66">a3</td> 
   <td class="xl66">b6</td> 
   <td class="xl66">c6</td> 
   <td class="xl66">d6</td> 
   <td class="xl66">d6</td> 
  </tr> 
  <tr height="19"> 
   <td class="xl66">a7</td> 
   <td class="xl66">b6</td> 
   <td class="xl66">c7</td> 
   <td class="xl66">d7</td> 
   <td class="xl66">e7</td> 
  </tr> 
  <tr height="19"> 
   <td class="xl66">a8</td> 
   <td class="xl66">a8</td> 
   <td class="xl66">c8</td> 
   <td class="xl66">d8</td> 
   <td class="xl66">d8</td> 
  </tr> <!--EndFragment--> 
 </tbody>
</table>
```
合并完表格内的单元格如下：
```html
  <table style="width:260pt"> <!--StartFragment--> 
 <colgroup>
  <col width="69" span="5" style="width:52pt"> 
 </colgroup>
 <tbody>
  <tr height="19"> 
   <td colspan="2" rowspan="2" class="xl65">R1</td> 
   <td rowspan="2" class="xl65">R2</td> 
   <td colspan="2" class="xl65">R3</td> 
  </tr> 
  <tr height="19"> 
   <td class="xl65">R31</td> 
   <td class="xl65">R32</td> 
  </tr> 
  <tr height="22"> 
   <td rowspan="4" class="xl73">a3</td> 
   <td rowspan="2" class="xl73">b3</td> 
   <td colspan="2" rowspan="2" class="xl73">c3</td> 
   <td class="xl73">e3</td> 
  </tr> 
  <tr height="19"> 
   <td class="xl73">e4</td> 
  </tr> 
  <tr height="19"> 
   <td class="xl73">a5</td> 
   <td class="xl73">c5</td> 
   <td class="xl73">d5</td> 
   <td class="xl73">e5</td> 
  </tr> 
  <tr height="19"> 
   <td class="xl73">b6</td> 
   <td class="xl73">c6</td> 
   <td class="xl73">d6</td> 
   <td class="xl73">d6</td> 
  </tr> 
  <tr height="19"> 
   <td class="xl73">a7</td> 
   <td class="xl73">b6</td> 
   <td class="xl73">c7</td> 
   <td class="xl73">d7</td> 
   <td class="xl73">e7</td> 
  </tr> 
  <tr height="19"> 
   <td colspan="2" class="xl73">a8</td> 
   <td class="xl73">c8</td> 
   <td class="xl73">d8</td> 
   <td class="xl73">d8</td> 
  </tr> <!--EndFragment--> 
 </tbody>
</table>
```

请先尝试使用element-plus实现，如果无法实现，再考虑使用原生table实现。
以下是element-plus的一些文档，可能有点帮助
多级表头合并的代码示例
```vue
<template>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column prop="date" label="Date" width="150" />
    <el-table-column label="Delivery Info">
      <el-table-column prop="name" label="Name" width="120" />
      <el-table-column label="Address Info">
        <el-table-column prop="state" label="State" width="120" />
        <el-table-column prop="city" label="City" width="120" />
        <el-table-column prop="address" label="Address" />
        <el-table-column prop="zip" label="Zip" width="120" />
      </el-table-column>
    </el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
const tableData = [
  {
    date: '2016-05-03',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
  },
  {
    date: '2016-05-04',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
  },
  {
    date: '2016-05-08',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
  },
  {
    date: '2016-05-06',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
  },
  {
    date: '2016-05-07',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
  },
]
</script>
```
以下是合并行与合并列的代码示例
```vue
<template>
  <div>
    <el-table
      :data="tableData"
      :span-method="arraySpanMethod"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="180" />
      <el-table-column prop="name" label="Name" />
      <el-table-column prop="amount1" sortable label="Amount 1" />
      <el-table-column prop="amount2" sortable label="Amount 2" />
      <el-table-column prop="amount3" sortable label="Amount 3" />
    </el-table>

    <el-table
      :data="tableData"
      :span-method="objectSpanMethod"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="id" label="ID" width="180" />
      <el-table-column prop="name" label="Name" />
      <el-table-column prop="amount1" label="Amount 1" />
      <el-table-column prop="amount2" label="Amount 2" />
      <el-table-column prop="amount3" label="Amount 3" />
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnCtx } from 'element-plus'

interface User {
  id: string
  name: string
  amount1: string
  amount2: string
  amount3: number
}

interface SpanMethodProps {
  row: User
  column: TableColumnCtx<User>
  rowIndex: number
  columnIndex: number
}

const arraySpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex,
}: SpanMethodProps) => {
  if (rowIndex % 2 === 0) {
    if (columnIndex === 0) {
      return [1, 2]
    } else if (columnIndex === 1) {
      return [0, 0]
    }
  }
}

const objectSpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex,
}: SpanMethodProps) => {
  if (columnIndex === 0) {
    if (rowIndex % 2 === 0) {
      return {
        rowspan: 2,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
}

const tableData: User[] = [
  {
    id: '12987122',
    name: 'Tom',
    amount1: '234',
    amount2: '3.2',
    amount3: 10,
  },
  {
    id: '12987123',
    name: 'Tom',
    amount1: '165',
    amount2: '4.43',
    amount3: 12,
  },
  {
    id: '12987124',
    name: 'Tom',
    amount1: '324',
    amount2: '1.9',
    amount3: 9,
  },
  {
    id: '12987125',
    name: 'Tom',
    amount1: '621',
    amount2: '2.2',
    amount3: 17,
  },
  {
    id: '12987126',
    name: 'Tom',
    amount1: '539',
    amount2: '4.1',
    amount3: 15,
  },
]
</script>
```