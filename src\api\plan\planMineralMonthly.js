import request from '@/utils/request'

// 查询选矿整体月计划列表
export function listPlanMineralMonthly(query) {
  return request({
    url: '/plan/planMineralMonthly/list',
    method: 'get',
    params: query
  })
}

// 查询选矿整体月计划详细
export function getPlanMineralMonthly(id) {
  return request({
    url: '/plan/planMineralMonthly/' + id,
    method: 'get'
  })
}

// 新增选矿整体月计划
export function addPlanMineralMonthly(data) {
  return request({
    url: '/plan/planMineralMonthly',
    method: 'post',
    data: data
  })
}

// 修改选矿整体月计划
export function updatePlanMineralMonthly(data) {
  return request({
    url: '/plan/planMineralMonthly',
    method: 'put',
    data: data
  })
}

// 删除选矿整体月计划
export function delPlanMineralMonthly(id) {
  return request({
    url: '/plan/planMineralMonthly/' + id,
    method: 'delete'
  })
}
