<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsOut02 } from '@/apis/data'
import type { TDataMuckingOutTotalWithPlanStats } from '@/apis/model'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDataMuckingOutTotalWithPlanStats[]>([])
const lastViewType = ref('monthly')

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate
    }

    const res = await apiDataStatsOut02(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取采场出矿计划完成度数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: {
        type: 'value'
      },
      series: []
    }
  }

  const xAxisData = chartData.value.map((item: TDataMuckingOutTotalWithPlanStats) => formatDisplayDate(item))
  const planData = chartData.value.map((item: TDataMuckingOutTotalWithPlanStats) => item.planTons || 0)
  const actualData = chartData.value.map((item: TDataMuckingOutTotalWithPlanStats) => item.totalTons || 0)
  
  // 计算完成率
  const completionRateData = chartData.value.map((item: TDataMuckingOutTotalWithPlanStats) => {
    const plan = item.planTons || 0
    const actual = item.totalTons || 0
    return plan > 0 ? Math.round((actual / plan) * 100 * 100) / 100 : 0
  })

  const getMax = (arr: number[]) => {
    let max = Math.round(Math.max(...arr))
    return max > 0 ? (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1) : 100
  }
  
  const y1Max = getMax([...planData, ...actualData])
  const y2Max = Math.max(100, Math.max(...completionRateData) * 1.2)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      formatter: function(params: any) {
        let result = params[0].axisValue + '<br/>';
        params.forEach((item: any) => {
          const value = item.value;
          const seriesName = item.seriesName;
          const color = item.color;
          let unit = '';

          if (seriesName === '计划出矿量' || seriesName === '实际出矿量') {
            unit = '吨';
          } else if (seriesName === '完成率') {
            unit = '%';
          }

          result += `<div style="display:flex;align-items:center;margin:5px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:${color};border-radius:50%;margin-right:5px;"></span>
            ${seriesName}: <strong>${value}${unit}</strong>
          </div>`;
        });
        return result;
      }
    },
    legend: {
      data: ['计划出矿量', '实际出矿量', '完成率'],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: ['5%', '5%'],
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          rotate: 0,
          margin: 15,
          overflow: 'truncate',
          formatter: (value: string) => {
            if (lastViewType.value === 'daily') {
              return value
            } else if (lastViewType.value === 'monthly') {
              return value.replace('年', '年\n')
            }
            return value
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '出矿量(吨)',
        min: 0,
        max: y1Max,
        interval: y1Max / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#5B9BD5',
          },
        },
        nameTextStyle: {
          color: '#5B9BD5',
        },
      },
      {
        type: 'value',
        name: '完成率(%)',
        min: 0,
        max: y2Max,
        interval: y2Max / 5,
        position: 'right',
        axisLabel: {
          formatter: '{value}%',
        },
        axisLine: {
          lineStyle: {
            color: '#70AD47',
          },
        },
        nameTextStyle: {
          color: '#70AD47',
        },
      },
    ],
    series: [
      {
        name: '计划出矿量',
        type: 'bar',
        barWidth: '30%',
        yAxisIndex: 0,
        data: planData,
        itemStyle: {
          color: '#5B9BD5',
        },
      },
      {
        name: '实际出矿量',
        type: 'bar',
        barWidth: '30%',
        yAxisIndex: 0,
        data: actualData,
        itemStyle: {
          color: '#E6A23C',
        },
      },
      {
        name: '完成率',
        type: 'line',
        yAxisIndex: 1,
        data: completionRateData,
        itemStyle: {
          color: '#70AD47',
        },
        lineStyle: {
          width: 2,
          color: '#70AD47',
        },
        symbol: 'circle',
        symbolSize: 6,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          color: '#333',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
    ],
  }
})
</script>
