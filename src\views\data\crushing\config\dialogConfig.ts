export default (): BaseFormProps => {
  return {
    itemStyle: {
      padding: '0px 0px 0px 0px',
    },
    rules: {
      workingPeriodId: [
        { required: true, message: '作业时段不能为空', trigger: 'blur' },
      ],
      operationDate: [
        { required: true, message: '作业日期不能为空', trigger: 'blur' },
      ],
    },
    formItems: [
      {
        field: 'operationDate',
        label: '作业日期',
        config: {
          clearable: false,
          type: 'date',
          disabledDate: (time: Date) => {
            return time.getTime() > Date.now()
          },
        },
        type: 'datepicker',
      },
      {
        field: 'workingPeriodId',
        type: 'select',
        options: [],
        label: '作业时段',
      },
      {
        field: 'operationTime',
        type: 'input',
        label: '运行时间',
      },
      {
        field: 'faultTime',
        type: 'input',
        label: '故障时长',
      },
      {
        field: 'crushingVolume',
        type: 'input',
        label: '破碎量',
      },
      {
        field: 'faultReason',
        type: 'input',
        label: '故障原因',
      },
      {
        field: 'faultStartTime',
        type: 'custom',
        label: '故障开始时间',
        slotNames: ['custom'],
      },
      {
        field: 'faultEndTime',
        type: 'custom',
        label: '故障结束时间',
        slotNames: ['custom'],
      },
    ],
    colLayout: {
      xl: 24,
      lg: 24,
      md: 24,
      sm: 24,
      xs: 24,
    },
    elFormConfig: {
      labelWidth: '100px',
    },
  }
}
