<template>
  <div class="flex items-center">
    <el-radio-group v-model="viewType" @change="handleDateTypeChange">
      <el-radio-button v-if="showViewType('daily')" label="daily"
        >每日</el-radio-button
      >
      <el-radio-button v-if="showViewType('weekly')" label="weekly"
        >每周</el-radio-button
      >
      <el-radio-button v-if="showViewType('monthly')" label="monthly"
        >每月</el-radio-button
      >
      <el-radio-button v-if="showViewType('yearly')" label="yearly"
        >每年</el-radio-button
      >
    </el-radio-group>

    <el-date-picker
      v-model="selectedDate"
      :type="getPickerType()"
      :format="getDateFormat()"
      :value-format="getValueFormat()"
      :placeholder="getPlaceholder()"
      class="ml-3"
    />

    <el-button type="primary" size="default" class="!ml-2" @click="triggerChange">
      查询
    </el-button>
    <el-button size="default" @click="handleReset">重置</el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import dayjs from 'dayjs'

type DateType = 'daily' | 'weekly' | 'monthly' | 'yearly'

const props = defineProps({
  defaultDate: {
    type: Object,
    default: () => ({
      viewType: 'daily',
      date: '',
    }),
  },
  modelValue: {
    type: Object,
    default: () => ({
      viewType: 'daily',
      date: '',
    }),
  },
  allowViewTypes: {
    type: Array as () => Array<DateType>,
    default: () => ['daily', 'weekly', 'monthly', 'yearly'],
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const viewType = ref<DateType>('daily')

// 检查视图类型是否显示
const showViewType = (type: DateType) => {
  return props.allowViewTypes.includes(type)
}

const selectedDate = ref<string>('')

// 设置默认日期
const setViewTypeDefaultDate = () => {
  const today = dayjs()
  if (viewType.value === 'daily') {
    selectedDate.value = '2025-06-12' // TODO
    // selectedDate.value = today.format('YYYY-MM-DD')
  } else if (viewType.value === 'weekly') {
    selectedDate.value = today.startOf('week').format('YYYY-MM-DD')
  } else if (viewType.value === 'yearly') {
    selectedDate.value = today.startOf('year').format('YYYY-MM-DD')
  } else {
    selectedDate.value = today.startOf('month').format('YYYY-MM-DD')
  }

  triggerChange()
}

// 处理日期类型切换
const handleDateTypeChange = () => {
  setViewTypeDefaultDate()
}

// 获取选择器类型
const getPickerType = () => {
  if (viewType.value === 'daily') return 'date'
  if (viewType.value === 'weekly') return 'week'
  if (viewType.value === 'yearly') return 'year'
  return 'month'
}

// 获取日期格式
const getDateFormat = () => {
  if (viewType.value === 'daily') return 'YYYY年MM月DD日'
  if (viewType.value === 'weekly') return 'YYYY年ww周'
  if (viewType.value === 'yearly') return 'YYYY年'
  return 'YYYY年MM月'
}

// 获取值格式
const getValueFormat = () => {
  // if (viewType.value === 'daily') return 'YYYY-MM-DD'
  // if (viewType.value === 'weekly') return 'YYYY-MM-DD'
  // if (viewType.value === 'yearly') return 'YYYY-MM-DD'
  return 'YYYY-MM-DD'
}

// 获取日期占位符
const getPlaceholder = () => {
  if (viewType.value === 'daily') return '选择日期'
  if (viewType.value === 'weekly') return '选择周'
  if (viewType.value === 'yearly') return '选择年份'
  return '选择月份'
}

const handleReset = () => {
  if (props.defaultDate && props.defaultDate.date && props.defaultDate.viewType) {
    selectedDate.value = props.defaultDate.date
    viewType.value = props.defaultDate.viewType
  } else {
    setViewTypeDefaultDate()
  }
  triggerChange()
}

// 触发change事件
const triggerChange = () => {
  const date = selectedDate.value

  if (!date) return

  const result = {
    viewType: viewType.value,
    date: viewType.value === 'monthly'
      ? dayjs(date).startOf('month').format('YYYY-MM-DD')
      : viewType.value === 'weekly'
        ? dayjs(date).startOf('week').format('YYYY-MM-DD')
        : viewType.value === 'yearly'
          ? dayjs(date).startOf('year').format('YYYY-MM-DD')
          : date,
  }

  emit('update:modelValue', result)
  emit('change', result)
}

// 监听传入的 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (
      newVal &&
      newVal.viewType &&
      newVal.date &&
      (newVal.viewType !== viewType.value ||
        newVal.date !== selectedDate.value)
    ) {
      // 确保视图类型在允许的范围内
      if (props.allowViewTypes.includes(newVal.viewType)) {
        viewType.value = newVal.viewType
      } else if (props.allowViewTypes.length > 0) {
        // 如果不在允许范围内，则使用第一个允许的类型
        viewType.value = props.allowViewTypes[0]
      }
      selectedDate.value = newVal.date
      triggerChange()
    } else if (!selectedDate.value) {
      // 如果没有传入值且当前没有值，则设置默认值
      setViewTypeDefaultDate()
    }
  },
  { immediate: true }
)
</script>
