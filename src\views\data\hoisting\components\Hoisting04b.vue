<template>
  <div class="w-full">
    <!-- 饼图区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6 my-6">
      <div v-for="(chart, index) in chartConfigs" :key="index">
        <BaseEchart v-if="chart.options" :options="chart.options" height="300px" />
        <div class="text-center text-gray-500 mt-2">{{ chart.title }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { apiDataStatsHoisting04b } from '@/apis/data'

interface Props {
  date: {
    viewType: string
    date: string
  }
}

const props = defineProps<Props>()

const chartData = ref<any[]>([])

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, date } = props.date
    if (!date) {
      return
    }

    const res = await apiDataStatsHoisting04b({
      viewType,
      startDate: date,
      endDate: date,
    })

    chartData.value = res || []
  } catch (error) {
    console.error('获取运行时间数据失败:', error)
  }
}

// 监听日期范围变化
watch(() => props.date, () => {
  if (props.date?.date) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})


// 按日期和时段分组数据
const groupedData = computed(() => {
  const groups: Record<string, { operationTime: number; faultTime: number }> =
    {}

  chartData.value.forEach((item) => {
    const period = item.workingPeriodName || '未知时段'

    if (!groups[period]) {
      groups[period] = {
        operationTime: 0,
        faultTime: 0,
      }
    }

    groups[period].operationTime += (Number(item.totalOperationTime) || 0) / 60
    groups[period].faultTime += (Number(item.totalFaultTime) || 0) / 60
  })

  return groups
})

// 计算总计
const totalData = computed(() => {
  return Object.values(groupedData.value).reduce(
    (acc, curr) => {
      return {
        operationTime: acc.operationTime + (curr.operationTime || 0),
        faultTime: acc.faultTime + (curr.faultTime || 0),
      }
    },
    { operationTime: 0, faultTime: 0 }
  )
})

// 图表配置
const chartConfigs = computed(() => {
  const timeSlots = [
    { title: '总时间统计', key: 'total' },
    { title: '0-8时', key: '0-8时' },
    { title: '8-20时', key: '8-20时' },
    { title: '20-0时', key: '20-0时' },
  ]

  return timeSlots.map((slot) => {
    let data
    if (slot.key === 'total') {
      data = totalData.value
    } else {
      data = groupedData.value[slot.key] || { operationTime: 0, faultTime: 0 }
    }

    return {
      title: slot.title,
      options: generatePieOptions(data.operationTime, data.faultTime),
    }
  })
})

// 生成饼图配置
const generatePieOptions = (runningTime: number, faultTime: number) => {
  const total = runningTime + faultTime

  return {
    grid: {
      left: 0,
      right: 0,
      bottom: '10%',
      top: '10%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.name}<br/>${params.value.toFixed(1)}小时<br/>${params.percent}%`
      },
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      left: 'center',
      data: ['运行时间', '故障处理时间'],
      itemGap: 20,
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 12,
        padding: [0, 5, 0, 0],
      },
    },
    series: [
      {
        name: '时间统计',
        type: 'pie',
        radius: ['30%', '50%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: true,
        padAngle: 2,
        itemStyle: {
          borderRadius: 5,
        },
        label: {
          show: true,
          formatter: function (params: any) {
            return `{b|${params.name}\n${params.value.toFixed(1)}小时}`
          },
          rich: {
            b: {
              fontSize: 12,
              lineHeight: 20,
              color: '#333',
            },
            d: {
              color: '#999',
              fontSize: 12,
              lineHeight: 20,
            },
          },
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 15,
          smooth: 0.2,
          lineStyle: {
            width: 1,
            type: 'solid',
          },
        },
        data: [
          {
            value: runningTime,
            name: '运行时间',
            itemStyle: {
              color: '#409EFF',
            },
            label: {
              formatter: `${runningTime.toFixed(1)}小时\n${((runningTime / total) * 100).toFixed(1)}%`,
            },
          },
          {
            value: faultTime,
            name: '故障处理时间',
            itemStyle: {
              color: '#F56C6C',
            },
            label: {
              formatter: `${faultTime.toFixed(1)}小时\n${((faultTime / total) * 100).toFixed(1)}%`,
            },
          },
        ],
      },
    ],
  }
}

</script>
