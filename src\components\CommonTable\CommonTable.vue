<template>
  <div class="common-table" v-loading="loading">
    <table
      class="custom-table"
      :class="{ 'no-header': props.hideHeader }"
      border="1"
      cellspacing="0"
      cellpadding="4"
    >
      <!-- 表头 -->
      <thead v-if="!props.hideHeader">
        <tr v-for="(row, rowIndex) in headerRows" :key="rowIndex">
          <th
            v-for="(cell, cellIndex) in row"
            :key="cellIndex"
            :colspan="cell.colspan"
            :rowspan="cell.rowspan"
            :class="cell.isHeader ? 'header-cell' : 'data-cell'"
          >
            {{ cell.label }}
          </th>
        </tr>
      </thead>
      <!-- 表体 -->
      <tbody>
        <tr v-for="(row, rowIndex) in processedTableData" :key="rowIndex">
          <td
            v-for="(cell, cellIndex) in row"
            :key="cellIndex"
            :colspan="cell.colspan"
            :rowspan="cell.rowspan"
            :class="[
              cell.hidden ? 'hidden-cell' : 'data-cell',
              { 'bold-cell': cell.isBold },
            ]"
            :style="getCellStyle(cell, cellIndex, rowIndex)"
            v-show="!cell.hidden"
          >
            {{ cell.value }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script lang="ts" setup>
import { getTableConfig, getTableData } from '@/apis/common-table'
import { ref, computed, watch } from 'vue'

// 定义接口类型
interface HeaderColumn {
  label: string
  prop?: string
  children?: HeaderColumn[]
  width?: number
}

interface MergeRule {
  mergeType: 'row' | 'col'
  column?: string
  columns?: string[]
  relateColumn?: string | null
}

interface TableConfig {
  headers: HeaderColumn[]
  mergeRules: MergeRule[]
}

interface HeaderCell {
  label: string
  colspan: number
  rowspan: number
  isHeader: boolean
}

interface DataCell {
  value: any
  colspan: number
  rowspan: number
  hidden: boolean
  prop: string
  isBold: boolean
}

// Props
interface Props {
  code: string
  hideHeader?: boolean
  params: any
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const tableConfig = ref<TableConfig>({
  headers: [],
  mergeRules: [],
})
const tableData = ref<any[]>([])

// 计算属性：扁平化列配置
const flatColumns = computed(() => {
  const result: Array<{ prop: string; label: string; width?: number }> = []

  const flatten = (columns: HeaderColumn[]) => {
    columns.forEach((column) => {
      if (column.children && column.children.length > 0) {
        flatten(column.children)
      } else if (column.prop) {
        result.push({
          prop: column.prop,
          label: column.label,
          width: column.width,
        })
      }
    })
  }

  flatten(tableConfig.value.headers)
  return result
})

// 计算单元格宽度（用于合并单元格）
const calculateCellWidth = (startIndex: number, colspan: number) => {
  let totalWidth = 0

  for (let i = startIndex; i < startIndex + colspan && i < flatColumns.value.length; i++) {
    const column = flatColumns.value[i]
    if (column.width && column.width > 0) {
      totalWidth += column.width
    }
  }

  return totalWidth > 0 ? `${totalWidth}%` : undefined
}

// 获取单元格样式
const getCellStyle = (cell: DataCell, cellIndex: number, rowIndex: number) => {
  const style: any = {
    fontWeight: cell.isBold ? 'bold' : 'normal'
  }

  // 如果表头隐藏且是第一行数据，需要设置宽度
  if (rowIndex === 0 && !cell.hidden) {
    // 计算当前单元格在扁平化列中的起始索引
    let currentIndex = 0
    const currentRow = processedTableData.value[rowIndex]

    for (let i = 0; i < cellIndex; i++) {
      if (!currentRow[i].hidden) {
        currentIndex += currentRow[i].colspan
      }
    }

    // 计算合并单元格的总宽度
    const width = calculateCellWidth(currentIndex, cell.colspan)
    if (width) {
      style.width = width
    }
  }

  return style
}

// 计算表头行结构
const headerRows = computed(() => {
  if (!tableConfig.value.headers.length) return []

  // 计算表头的最大深度
  const getMaxDepth = (columns: HeaderColumn[], depth = 1): number => {
    let maxDepth = depth
    columns.forEach((column) => {
      if (column.children && column.children.length > 0) {
        maxDepth = Math.max(maxDepth, getMaxDepth(column.children, depth + 1))
      }
    })
    return maxDepth
  }

  const maxDepth = getMaxDepth(tableConfig.value.headers)
  const rows: HeaderCell[][] = Array.from({ length: maxDepth }, () => [])

  // 递归构建表头结构
  const buildHeader = (columns: HeaderColumn[], currentDepth: number) => {
    columns.forEach((column) => {
      if (column.children && column.children.length > 0) {
        // 计算子列数量
        const getLeafCount = (col: HeaderColumn): number => {
          if (!col.children || col.children.length === 0) return 1
          return col.children.reduce(
            (sum, child) => sum + getLeafCount(child),
            0
          )
        }

        const leafCount = getLeafCount(column)

        // 检查是否所有子级都与父级label相同
        const allChildrenSameLabel = column.children.every(
          (child) => child.label === column.label
        )

        rows[currentDepth].push({
          label: column.label,
          colspan: leafCount,
          rowspan: allChildrenSameLabel ? maxDepth - currentDepth : 1,
          isHeader: true,
        })

        // 如果不是所有子级都与父级相同，则继续构建子级
        if (!allChildrenSameLabel) {
          buildHeader(column.children, currentDepth + 1)
        }
      } else {
        rows[currentDepth].push({
          label: column.label,
          colspan: 1,
          rowspan: maxDepth - currentDepth,
          isHeader: true,
        })
      }
    })
  }

  buildHeader(tableConfig.value.headers, 0)

  // 过滤掉空行
  return rows.filter((row) => row.length > 0)
})

// 处理表格数据，应用合并规则
const processedTableData = computed(() => {
  if (!tableData.value.length || !flatColumns.value.length) return []

  const result: DataCell[][] = []

  // 初始化数据结构
  tableData.value.forEach((row) => {
    const processedRow: DataCell[] = []
    const boldFields = row.bold || [] // 获取当前行的bold字段数组

    flatColumns.value.forEach((column) => {
      processedRow.push({
        value: row[column.prop],
        colspan: 1,
        rowspan: 1,
        hidden: false,
        prop: column.prop,
        isBold: boldFields.includes(column.prop), // 检查当前字段是否在bold数组中
      })
    })
    result.push(processedRow)
  })

  // 应用行合并规则
  tableConfig.value.mergeRules.forEach((rule) => {
    if (rule.mergeType === 'row' && rule.column) {
      applyRowMerge(result, rule.column, rule.relateColumn || null)
    }
  })

  // 应用列合并规则
  tableConfig.value.mergeRules.forEach((rule) => {
    if (rule.mergeType === 'col' && rule.columns) {
      applyColMerge(result, rule.columns)
    }
  })

  return result
})

// 应用行合并
const applyRowMerge = (
  data: DataCell[][],
  column: string,
  relateColumn: string | null
) => {
  const colIndex = flatColumns.value.findIndex((col) => col.prop === column)
  const relateColIndex = relateColumn
    ? flatColumns.value.findIndex((col) => col.prop === relateColumn)
    : -1

  if (colIndex === -1) return

  let startRow = 0

  while (startRow < data.length) {
    const startValue = data[startRow][colIndex].value
    const startRelateValue =
      relateColIndex >= 0 ? data[startRow][relateColIndex].value : null

    // 跳过空值
    if (!startValue) {
      startRow++
      continue
    }

    // 检查关联列条件
    if (relateColumn && !startRelateValue) {
      startRow++
      continue
    }

    let endRow = startRow

    // 查找连续相同的值
    for (let i = startRow + 1; i < data.length; i++) {
      const currentValue = data[i][colIndex].value
      const currentRelateValue =
        relateColIndex >= 0 ? data[i][relateColIndex].value : null

      if (
        currentValue === startValue &&
        (!relateColumn || currentRelateValue === startRelateValue)
      ) {
        endRow = i
      } else {
        break
      }
    }

    // 如果有合并的行
    if (endRow > startRow) {
      data[startRow][colIndex].rowspan = endRow - startRow + 1

      // 隐藏被合并的单元格
      for (let i = startRow + 1; i <= endRow; i++) {
        data[i][colIndex].hidden = true
      }
    }

    startRow = endRow + 1
  }
}

// 应用列合并
const applyColMerge = (data: DataCell[][], columns: string[]) => {
  const colIndexes = columns
    .map((col) => flatColumns.value.findIndex((c) => c.prop === col))
    .filter((idx) => idx >= 0)

  if (colIndexes.length < 2) return

  data.forEach((row) => {
    let startCol = 0

    while (startCol < colIndexes.length) {
      const startColIndex = colIndexes[startCol]
      const startValue = row[startColIndex].value

      // 跳过空值或已隐藏的单元格
      if (!startValue || row[startColIndex].hidden) {
        startCol++
        continue
      }

      let endCol = startCol

      // 查找连续相同的值
      for (let i = startCol + 1; i < colIndexes.length; i++) {
        const currentColIndex = colIndexes[i]
        const currentValue = row[currentColIndex].value

        if (currentValue === startValue && !row[currentColIndex].hidden) {
          endCol = i
        } else {
          break
        }
      }

      // 如果有合并的列
      if (endCol > startCol) {
        row[startColIndex].colspan = endCol - startCol + 1

        // 隐藏被合并的单元格
        for (let i = startCol + 1; i <= endCol; i++) {
          const colIndex = colIndexes[i]
          row[colIndex].hidden = true
        }
      }

      startCol = endCol + 1
    }
  })
}

// 处理列宽度配置
const processColumnWidths = (headers: HeaderColumn[]) => {
  // 获取所有最底层的列（叶子节点）
  const getLeafColumns = (columns: HeaderColumn[]): HeaderColumn[] => {
    const leafColumns: HeaderColumn[] = []

    const traverse = (cols: HeaderColumn[]) => {
      cols.forEach(col => {
        if (col.children && col.children.length > 0) {
          traverse(col.children)
        } else {
          leafColumns.push(col)
        }
      })
    }

    traverse(columns)
    return leafColumns
  }

  const leafColumns = getLeafColumns(headers)

  // 计算已设置宽度的总和
  let totalSetWidth = 0
  let unsetWidthCount = 0

  leafColumns.forEach(col => {
    if (col.width && col.width > 0) {
      totalSetWidth += col.width
    } else {
      unsetWidthCount++
    }
  })

  // 计算剩余宽度并平均分配给未设置宽度的列
  if (unsetWidthCount > 0) {
    const remainingWidth = Math.max(0, 100 - totalSetWidth)
    const averageWidth = remainingWidth / unsetWidthCount

    leafColumns.forEach(col => {
      if (!col.width || col.width <= 0) {
        col.width = averageWidth
      }
    })
  }

  return headers
}

// 加载表格配置
const loadTableConfig = async (code: string) => {
  loading.value = true
  try {
    const response = await getTableConfig(code)
    // 处理列宽度配置
    const processedHeaders = processColumnWidths(response.data.headers)
    tableConfig.value = {
      ...response.data,
      headers: processedHeaders
    }
  } catch (error) {
    console.error('加载表格配置失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载表格数据
const loadTableData = async (code: string) => {
  loading.value = true
  try {
    const response = await getTableData(code, props.params)
    tableData.value = response.data
  } catch (error) {
    console.error('加载表格数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化
const init = async () => {
  await Promise.all([loadTableConfig(props.code), loadTableData(props.code)])
}

// 监听code变化
watch(
  [() => props.code, () => props.params],
  () => {
    init()
  },
  { immediate: true, deep: true }
)

// onMounted(() => {
//   init()
// })
</script>

<style scoped>
.common-table {
  width: 100%;
  overflow-x: auto;
}

.custom-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.custom-table th,
.custom-table td {
  border: 1px solid #dcdfe6;
  text-align: center;
  vertical-align: middle;
  min-width: 80px;
}

.header-cell {
  background-color: #f5f7fa;
  font-weight: bold;
  color: #606266;
  padding: 8px 12px;
}

.data-cell {
  background-color: #ffffff;
  color: #606266;
  padding: 8px 12px;
}

.hidden-cell {
  display: none;
}

/* .custom-table tr:nth-child(even) .data-cell {
  background-color: #fafafa;
} */

.custom-table tr:hover .data-cell {
  background-color: #f5f7fa;
}

.bold-cell {
  font-weight: bold !important;
}

/* 隐藏表头时的样式调整 */
.custom-table.no-header {
  border-top: none;
}

.custom-table.no-header tbody tr:first-child td {
  border-top: none;
}
</style>
