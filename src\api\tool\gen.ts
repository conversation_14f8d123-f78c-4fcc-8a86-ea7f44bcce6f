import { request } from '@/utils/hsj/service/index'

// 查询db数据库列表
export function listDbTable(query: anyObj) {
  return request({
    url: '/tool/gen/db/list',
    method: 'get',
    params: query,
  })
}

// 查询表详细信息
export function getGenTable(tableId: StrNum) {
  return request({
    url: '/tool/gen/' + tableId,
    method: 'get',
  })
}

// 修改代码生成信息
export function updateGenTable(data: anyObj) {
  return request({
    url: '/tool/gen',
    method: 'put',
    data: data,
  })
}

// 导入表
export function importTable(data: anyObj) {
  return request({
    url: '/tool/gen/importTable',
    method: 'post',
    params: data,
  })
}

// 预览生成代码
export function previewTable(tableId: number) {
  return request<GenPreviewApi>({
    url: '/tool/gen/preview/' + tableId,
    method: 'get',
  })
}

// 生成代码（自定义路径）
export function genCode(tableName: string) {
  return request({
    url: '/tool/gen/genCode/' + tableName,
    method: 'get',
  })
}

// 同步数据库
export function synchDb(tableName: string) {
  return request({
    url: '/tool/gen/synchDb/' + tableName,
    method: 'get',
  })
}
