import request from '@/utils/request'

// 查询采矿整体月计划列表
export function listPlanMiningMonthly(query) {
  return request({
    url: '/plan/planMiningMonthly/list',
    method: 'get',
    params: query
  })
}

// 查询采矿整体月计划详细
export function getPlanMiningMonthly(id) {
  return request({
    url: '/plan/planMiningMonthly/' + id,
    method: 'get'
  })
}

// 新增采矿整体月计划
export function addPlanMiningMonthly(data) {
  return request({
    url: '/plan/planMiningMonthly',
    method: 'post',
    data: data
  })
}

// 修改采矿整体月计划
export function updatePlanMiningMonthly(data) {
  return request({
    url: '/plan/planMiningMonthly',
    method: 'put',
    data: data
  })
}

// 删除采矿整体月计划
export function delPlanMiningMonthly(id) {
  return request({
    url: '/plan/planMiningMonthly/' + id,
    method: 'delete'
  })
}
