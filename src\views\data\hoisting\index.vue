<script setup name="Hoisting" lang="ts">
import { dataBaseUrl } from '@/api/config/base.js'
import { DictOptions, getPeriodList } from '@/dict'
import getComputedConfig from '@/hooks/getPageConfig'
import useDialog from '@/hooks/useDialog'
import { proxy } from '@/utils/provide'
import BatchAddDialog from './components/BatchAddDialog.vue'
import getContentConfig from './config/contentConfig'
import getDialogConfig from './config/dialogConfig'
import getSearchConfig from './config/searchConfig'
import ExcelStepImport from '@/components/ExcelStepImport'

const pageName = 'hoisting'
const requestBaseUrl = dataBaseUrl
const pageSearchRef = useTemplateRef('pageSearchRef')
const pageContentRef = useTemplateRef('pageContentRef')
const dialogHideItems = ref<string[]>([])
const tableHideItems = ref<string[]>([])
const periodOptions = ref<DictOptions[]>([])

const initDict = async () => {
  periodOptions.value = await getPeriodList()
}
initDict()

const dictMap = ref({
  workingPeriodId: periodOptions,
})

const searchConfig = getSearchConfig()
const searchConfigComputed = computed(() => {
  return getComputedConfig(searchConfig, dictMap)
})
const tableSelected = ref<any[]>([])
const tableListener = {
  selectionChange: (selected: any) => {
    tableSelected.value = selected
  },
}
const contentConfig = getContentConfig()
const contentConfigComputed = computed(() => {
  contentConfig.hideItems = tableHideItems
  return contentConfig
})

const dialogConfig = getDialogConfig()

const dialogConfigComputed = computed(() => {
  dialogConfig.hideItems = dialogHideItems
  return getComputedConfig(dialogConfig, dictMap)
})

const addCallBack = () => {
  dialogHideItems.value.length = 0
}
const editCallBack = (_item: any, type: any, _res: any) => {
  isEditMore.value = type
}
const isEditMore = ref(false)
const editMoreClick = () => {
  if (tableSelected.value.length > 0) {
    const data = tableSelected.value[0]
    pageContentRef.value?.editClick(data, true)
    nextTick(() => {
      const newArray = tableSelected.value.slice(1)
      dialogRef.value?.changeSelected(newArray)
    })
  }
}

const editNext = (data: any) => {
  pageContentRef.value?.editClick(data, true)
}

const { dialogRef, infoInit, addClick, editBtnClick } = useDialog(
  addCallBack,
  editCallBack,
  '添加'
)

const dialogWidth = ref('700px')
const searchData = computed(() => {
  return pageContentRef.value?.finalSearchData
})

const search = () => {
  pageSearchRef.value?.search()
}

const beforeSend = (queryInfo: anyObj) => {
  // Process 作业日期 date range
  if (queryInfo.operationDate && Array.isArray(queryInfo.operationDate)) {
    const dateRange = queryInfo.operationDate
    queryInfo['params[beginOperationDate]'] = dateRange[0]
    queryInfo['params[endOperationDate]'] = dateRange[1]
    delete queryInfo.operationDate
  }
}

const permission = ref({
  add: 'data:hoisting:add',
  edit: 'data:hoisting:edit',
  del: 'data:hoisting:remove',
})

// Batch add dialog control
const batchAddVisible = ref(false)
const handleBatchAdd = () => {
  batchAddVisible.value = true
}

const handleBatchAddRefresh = () => {
  search()
}

const onChangeShowColumn = (filterArr: string[]) => {
  tableHideItems.value = filterArr
}

// Excel import dialog control
const importVisible = ref(false)
const handleImport = () => {
  importVisible.value = true
}

const handleImportSuccess = () => {
  search()
}

const handleImportError = (error: any) => {
  console.error('Import error:', error)
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download(
    'data/hoisting/export',
    {
      ...searchData.value,
    },
    `提升数据_${new Date().getTime()}.xlsx`
  )
}
</script>
<template>
  <div class="default-main page">
    <PageSearch
      ref="pageSearchRef"
      :pageName="pageName"
      :searchConfig="searchConfigComputed"
    ></PageSearch>
    <PageContent
      ref="pageContentRef"
      :pageName="pageName"
      :contentConfig="contentConfigComputed"
      :dictMap="dictMap"
      :tableListener="tableListener"
      :tableSelected="tableSelected"
      :permission="permission"
      :requestBaseUrl="requestBaseUrl"
      @beforeSend="beforeSend"
      @addClick="addClick"
      @editBtnClick="editBtnClick"
      @onChangeShowColumn="onChangeShowColumn"
      @editMoreClick="editMoreClick"
    >
      <template #handleLeft>
        <el-button
          v-hasPermi="[permission.add]"
          type="primary"
          @click="handleBatchAdd"
        >
          <SvgIcon :size="14" iconClass="plus"></SvgIcon>
          <span class="ml6">按日添加</span>
        </el-button>
        <el-button
          class="order16"
          type="success"
          v-hasPermi="['data:hoisting:add']"
          @click="handleImport"
        >
          <SvgIcon size="14" iconClass="upload" />
          <span class="ml6">导入</span>
        </el-button>
        <el-button
          class="order17"
          type="warning"
          v-hasPermi="['data:hoisting:export']"
          @click="handleExport"
        >
          <SvgIcon size="14" iconClass="download" />
          <span class="ml6">导出</span>
        </el-button>
      </template>
    </PageContent>
    <PageDialog
      ref="dialogRef"
      sendIdKey="id"
      :width="getWidth(dialogWidth)"
      :pageName="pageName"
      :dialogConfig="dialogConfigComputed"
      :infoInit="infoInit"
      :search="search"
      :isEditMore="isEditMore"
      :requestBaseUrl="requestBaseUrl"
      @editNext="editNext"
    >
      <template #faultStartTimeCustom="{ backData }">
        <el-time-picker
          v-model="backData.formData.faultStartTime"
          placeholder="请选择故障开始时间"
          format="HH:mm"
          value-format="HH:mm:ss"
          style="width: 100%"
        />
      </template>
      <template #faultEndTimeCustom="{ backData }">
        <el-time-picker
          v-model="backData.formData.faultEndTime"
          placeholder="请选择故障结束时间"
          format="HH:mm"
          value-format="HH:mm:ss"
          style="width: 100%"
        />
      </template>
    </PageDialog>
    <BatchAddDialog
      v-model="batchAddVisible"
      :periodOptions="periodOptions"
      @refresh="handleBatchAddRefresh"
    />
    <ExcelStepImport
      v-model:visible="importVisible"
      templateKey="data_mine_hoisting"
      title="起重机数据导入"
      @success="handleImportSuccess"
      @error="handleImportError"
    />
  </div>
</template>

<style scoped lang="scss"></style>
