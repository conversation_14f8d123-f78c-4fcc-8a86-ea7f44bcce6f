<template>
  <div class="default-main page p-6 bg-white rounded shadow">
    <div class="mb-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="font-bold text-2xl text-gray-800">运行时间</h2>
        <div class="flex space-x-2">
          <el-button class="!px-4" icon="DataAnalysis">数据分析</el-button>
          <el-button class="!px-4" type="primary" icon="Setting" @click="goDataManagement">数据管理</el-button>
        </div>
      </div>

      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex flex-wrap items-center gap-2">
          <el-select 
            v-model="chartType" 
            placeholder="选择图表类型" 
            style="width: 150px"
            class="mr-4"
          >
            <el-option label="整体" value="02a" />
            <el-option label="作业时段" value="02b" />
          </el-select>
          <ChartDateRange1 v-model="chartDateRange" :allow-view-types="['daily', 'weekly', 'monthly']" />
        </div>
      </div>
    </div>

    <div class="w-full">
      <Hoisting02a 
        v-if="chartType === '02a'" 
        :date-range="chartDateRange" 
      />
      <Hoisting02b 
        v-if="chartType === '02b'" 
        :date-range="chartDateRange" 
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChartDateRange1 from '@/components/ChartDateRange/ChartDateRange1.vue'
import Hoisting02a from './components/Hoisting02a.vue'
import Hoisting02b from './components/Hoisting02b.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const goDataManagement = () => {
  router.push('/demo/actual_hoist_data')
}

// 图表类型选择
const chartType = ref('02a')

// 日期范围
const chartDateRange = ref({
  viewType: 'daily',
  startDate: '',
  endDate: '',
})
</script>
