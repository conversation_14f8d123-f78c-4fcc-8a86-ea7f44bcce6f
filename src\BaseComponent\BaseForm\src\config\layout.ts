import { FormItem } from '../types/index'
import { ColProps } from 'element-plus'

export default (
  item: FormItem,
  colLayout?: Partial<ColProps>
): Partial<ColProps> => {
  let layout: Partial<ColProps> = {}

  if (item.type.toLowerCase() === 'datepicker' && item.config) {
    if (
      'type' in item.config &&
      typeof item.config.type === 'string' &&
      item.config.type.includes('range')
    ) {
      layout = {
        xl: 5,
        lg: 8,
        md: 10,
        sm: 12,
        xs: 24,
      }
      return item.layout || colLayout || layout
    }
  }

  layout = {
    xl: 4,
    lg: 6,
    md: 8,
    sm: 12,
    xs: 24,
  }

  return item.layout || colLayout || layout
}
