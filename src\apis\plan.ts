// 自动生成的 API 文件，请勿手动修改

import { request, blob } from '@/utils/hsj/service/index'
import { AxiosResponse } from 'axios'
import { TPlanSupportMonthly, TPlanSupportMonthlyBatchDto, TPlanStopeMonthly, TPlanStopeMonthlyBatchDto, TPlanMiningMonthly, TPlanMiningMonthlyBatchDto, TPlanMineralSaleMonthly, TPlanMineralSaleMonthlyBatchDto, TPlanMineralMonthly, TPlanMineralMonthlyBatchDto, TPlanDthMonthly, TPlanDthMonthlyBatchDto, TPlanDriftMonthly, TPlanDriftMonthlyBatchDto, TPlanDeepHoleMonthly, TPlanDeepHoleMonthlyBatchDto } from './model'

export async function apiPlanPlanSupportMonthlyExport(query: TPlanSupportMonthly): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/plan/planSupportMonthly/export',
    method: 'post',
    params: query
  }); 
}


export async function apiPlanPlanSupportMonthlyBatch(data: TPlanSupportMonthlyBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/plan/planSupportMonthly/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiPlanPlanStopeMonthlyExport(query: TPlanStopeMonthly): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/plan/planStopeMonthly/export',
    method: 'post',
    params: query
  }); 
}


export async function apiPlanPlanStopeMonthlyBatch(data: TPlanStopeMonthlyBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/plan/planStopeMonthly/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiPlanPlanMiningMonthlyExport(query: TPlanMiningMonthly): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/plan/planMiningMonthly/export',
    method: 'post',
    params: query
  }); 
}


export async function apiPlanPlanMiningMonthlyBatch(data: TPlanMiningMonthlyBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/plan/planMiningMonthly/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiPlanPlanMineralSaleMonthlyExport(query: TPlanMineralSaleMonthly): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/plan/planMineralSaleMonthly/export',
    method: 'post',
    params: query
  }); 
}


export async function apiPlanPlanMineralSaleMonthlyBatch(data: TPlanMineralSaleMonthlyBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/plan/planMineralSaleMonthly/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiPlanPlanMineralMonthlyExport(query: TPlanMineralMonthly): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/plan/planMineralMonthly/export',
    method: 'post',
    params: query
  }); 
}


export async function apiPlanPlanMineralMonthlyBatch(data: TPlanMineralMonthlyBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/plan/planMineralMonthly/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiPlanPlanDthMonthlyExport(query: TPlanDthMonthly): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/plan/planDthMonthly/export',
    method: 'post',
    params: query
  }); 
}


export async function apiPlanPlanDthMonthlyBatch(data: TPlanDthMonthlyBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/plan/planDthMonthly/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiPlanPlanDriftMonthlyExport(query: TPlanDriftMonthly): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/plan/planDriftMonthly/export',
    method: 'post',
    params: query
  }); 
}


export async function apiPlanPlanDriftMonthlyBatch(data: TPlanDriftMonthlyBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/plan/planDriftMonthly/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


export async function apiPlanPlanDeepHoleMonthlyExport(query: TPlanDeepHoleMonthly): Promise<AxiosResponse<Blob>> {
  return blob({
    url: '/plan/planDeepHoleMonthly/export',
    method: 'post',
    params: query
  }); 
}


export async function apiPlanPlanDeepHoleMonthlyBatch(data: TPlanDeepHoleMonthlyBatchDto[]): Promise<string> {
  return request<{ data: string }>({
    url: '/plan/planDeepHoleMonthly/batch',
    method: 'post',
    data
  }).then(res => res.data); 
}


