<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsCrushing01b } from '@/apis/data'
import type { TDateCrushingOperationPeriodStats } from '@/apis/model'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDateCrushingOperationPeriodStats[]>([])
const lastViewType = ref('monthly')

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate,
    }

    const res = await apiDataStatsCrushing01b(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取破碎量数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 按日期和时段分组数据
const groupedData = computed(() => {
  const groups: Record<
    string,
    Record<string, { crushingVolume?: any; operationTime?: any; faultCount?: number }>
  > = {}

  chartData.value.forEach((item: TDateCrushingOperationPeriodStats) => {
    const dateKey = formatDisplayDate(item)

    if (!dateKey) return

    if (!groups[dateKey]) {
      groups[dateKey] = {}
    }

    const period = item.workingPeriodName || '未知时段'
    groups[dateKey][period] = {
      crushingVolume: item.totalCrushingVolume,
      operationTime: item.totalOperationTime,
      faultCount: item.faultCount,
    }
  })

  return groups
})

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: [],
      },
      yAxis: {
        type: 'value',
      },
      series: [],
    }
  }

  // 获取所有唯一的日期
  const dates = Object.keys(groupedData.value).sort()

  const periods = Array.from(
    new Set(
      Object.values(groupedData.value).flatMap((periods) =>
        Object.keys(periods)
      )
    )
  ).sort((a, b) => {
    const startA = Number(a.split('-')[0])
    const startB = Number(b.split('-')[0])
    return startA - startB
  })

  // 获取所有时段和类型组合
  const periodAndTypes = periods.reduce((acc, period) => {
    acc.push(period + '破碎量')
    acc.push(period + '作业时间')
    return acc
  }, [] as string[])

  let y0Max = 0
  let y1Max = 0

  const dataMap = periodAndTypes.reduce(
    (acc, periodAndType) => {
      const isCrushingVolume = periodAndType.endsWith('破碎量')
      const period = periodAndType.replace('破碎量', '').replace('作业时间', '')
      acc[periodAndType] = dates.map((date) => {
        const periodData = groupedData.value[date][period]
        if (!periodData) return 0
        return isCrushingVolume ? periodData.crushingVolume || 0 : periodData.operationTime || 0
      })

      if (isCrushingVolume) {
        y0Max = Math.max(y0Max, ...acc[periodAndType])
      } else {
        y1Max = Math.max(y1Max, ...acc[periodAndType])
      }
      return acc
    },
    {} as Record<string, number[]>
  )

  const getMax = (max: number) => {
    max = Math.round(max)
    return (
      (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1)
    )
  }
  y0Max = getMax(y0Max)
  y1Max = getMax(y1Max)

  // 生成系列数据
  const series = periodAndTypes.flatMap((periodAndType) => {
    const isCrushingVolume = periodAndType.endsWith('破碎量')
    const yAxisIndex = isCrushingVolume ? 0 : 1
    const type = isCrushingVolume ? 'bar' : 'line'
    const itemStyle = {
      color: getColorByPeriod(periodAndType),
    }

    const lineStyle = {
      color: getColorByPeriod(periodAndType),
      width: 2,
    }

    const data = dataMap[periodAndType]

    return {
      name: periodAndType,
      type,
      yAxisIndex,
      data,
      itemStyle,
      ...(type === 'line'
        ? {
            symbol: 'circle',
            symbolSize: 6,
            lineStyle,
          }
        : {
            barGap: 0,
            barWidth: '25%',
          }),
    }
  })

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: (params: any) => {
        const date = params[0].axisValue
        const periodData = groupedData.value[date] || {}

        let result = `<div style="margin-bottom: 5px;">${date}</div>`

        // 按时段分组显示
        const periodsInData = Array.from(new Set(periodAndTypes.map(item => item.replace('破碎量', '').replace('作业时间', ''))))

        periodsInData.forEach(period => {
          const crushingVolumeItem = periodData[period]?.crushingVolume
          const operationTimeItem = periodData[period]?.operationTime

          if (crushingVolumeItem !== undefined || operationTimeItem !== undefined) {
            result += `<div style="margin: 5px 0;">
              <div style="font-weight: bold;">${period}</div>
              ${crushingVolumeItem !== undefined ? `<div>破碎量: ${crushingVolumeItem}吨</div>` : ''}
              ${operationTimeItem !== undefined ? `<div>作业时间: ${operationTimeItem}小时</div>` : ''}
            </div>`
          }
        })

        return result
      },
    },
    legend: {
      data: periodAndTypes,
      bottom: 0,
      left: 'center',
      itemGap: 20,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 12,
        lineHeight: 16,
      },
    },
    grid: {
      left: '3%',
      right: '3%',
      top: '10%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: ['5%', '5%'],
      data: dates,
      axisLabel: {
        rotate: 0,
        margin: 15,
        overflow: 'truncate',
        formatter: (value: string) => {
          if (lastViewType.value === 'daily') {
            return value
          } else {
            return value
          }
        },
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '破碎量(吨)',
        min: 0,
        max: y0Max,
        interval: y0Max / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#5B9BD5',
          },
        },
        nameTextStyle: {
          color: '#5B9BD5',
        },
      },
      {
        type: 'value',
        name: '作业时间(小时)',
        min: 0,
        max: y1Max,
        interval: y1Max / 5,
        position: 'right',
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#70AD47',
          },
        },
        nameTextStyle: {
          color: '#70AD47',
        },
      },
    ],
    series,
  }
})

// 根据时段获取颜色
const getColorByPeriod = (period: string) => {
  const colorMap: Record<string, string> = {
    '0-8时破碎量': '#5B9BD5',
    '8-20时破碎量': '#70AD47',
    '20-0时破碎量': '#FFC000',
    '0-8时作业时间': '#70AD47',
    '8-20时作业时间': '#FFC000',
    '20-0时作业时间': '#5B9BD5',
  }

  return colorMap[period] || '#5B9BD5' // 默认颜色
}
</script>
