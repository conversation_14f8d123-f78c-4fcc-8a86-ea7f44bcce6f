import {
  FormProps,
  FormRules,
  RowProps,
  ColProps,
  FormItemProps,
  ElTooltipProps,
  InputProps,
  InputNumberProps,
  ElCascader,
  ElSelect,
  ElTree,
  ElTreeSelect,
  DatePickerProps,
  CheckboxProps,
  RadioProps,
} from 'element-plus'
import { Ref, ComponentPublicInstance } from 'vue'

// 定义选项类型
export interface OptionItem {
  value: any
  label: string
  key?: string | number
  disabled?: boolean
  [key: string]: any
}

// 定义事件函数类型
export type EventFunction = (event: Event, ...args: any[]) => void

// 定义选项函数类型
export type OptionFunction = (option: OptionItem, index: number) => void

// 定义权限类型
export type Permission = string | string[]

// 定义样式类型
export type CSSProperties = Record<string, string | number>

interface BaseFormItem extends Partial<FormItemProps> {
  field: string
  label: string
  isHidden?: boolean
  hideLabel?: boolean
  formItemConfig?: Partial<FormItemProps>
  tip?: string
  tipConfig?: Partial<ElTooltipProps>
  slotNames?: string[]
  eventFunction?: Record<string, EventFunction>
  options?: OptionItem[] | Ref<OptionItem[]>
  layout?: Partial<ColProps>
  isGroup?: boolean
  optionConfig?: Record<string, any>
  setValue?: string
  setLabel?: string
  optionFunction?: Record<string, OptionFunction>
  permission?: Permission
  default?: any
}

// 定义配置映射类型
type ConfigMap = {
  input: Partial<InputProps>
  inputNumber: Partial<InputNumberProps>
  textarea: Partial<InputProps>
  custom: Record<string, any>
  cascader: Partial<InstanceType<typeof ElCascader>['$props']>
  select: Partial<InstanceType<typeof ElSelect>['$props']>
  tree: Partial<InstanceType<typeof ElTree>['$props']>
  treeSelect: Partial<InstanceType<typeof ElTreeSelect>['$props']>
  datepicker: Partial<DatePickerProps>
  checkBox: Partial<CheckboxProps>
  radio: Partial<RadioProps>
}

// 定义通用的表单项接口
export interface FormItemTool<T extends keyof ConfigMap> extends BaseFormItem {
  type: T
  config?: ConfigMap[T]
}

// 定义联合类型
export type FormItem =
  | FormItemTool<'cascader'>
  | FormItemTool<'checkBox'>
  | FormItemTool<'custom'>
  | FormItemTool<'datepicker'>
  | FormItemTool<'input'>
  | FormItemTool<'inputNumber'>
  | FormItemTool<'radio'>
  | FormItemTool<'select'>
  | FormItemTool<'textarea'>
  | FormItemTool<'tree'>
  | FormItemTool<'treeSelect'>

export type FormItems = FormItem[]

// 定义表单数据类型
export type FormData = Record<string, any>

// 定义插槽数据类型
export interface SlotData {
  item: FormItem
  dataValue: any
  formData: FormData
  [key: string]: any
}

// 定义键盘事件类型
export interface KeyUpEnterEvent {
  event: KeyboardEvent
  current: FormItem
}

// 定义组件实例类型
export interface FormComponentInstance extends ComponentPublicInstance {
  getRef?: () => ComponentPublicInstance | null
}

// 定义引用集合类型
export interface AllRefs {
  [key: string]: ComponentPublicInstance | null
}

export interface Props {
  data?: FormData
  formItems: FormItems
  elFormConfig?: FormProps | Record<string, any>
  allDisabled?: boolean
  rowConfig?: Partial<RowProps>
  footerLayout?: Partial<ColProps>
  colLayout?: Partial<ColProps>
  itemStyle?: CSSProperties
  rules?: FormRules
  hideItems?: Ref<string[]> | string[]
}

// 定义插槽类型
export type FormSlots = Record<string, any>

// 定义事件类型
export interface FormEmits {
  keyUpEnter: [event: KeyUpEnterEvent]
}
