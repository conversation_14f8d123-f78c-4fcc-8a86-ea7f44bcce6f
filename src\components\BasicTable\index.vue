<template>
  <div class="basic-table" v-loading="loading">
    <table class="custom-table" border="1" cellspacing="0" cellpadding="4">
      <!-- 表头 -->
      <thead>
        <!-- 第一行表头 -->
        <tr>
          <th colspan="2" rowspan="2">R1</th>
          <th rowspan="2">R2</th>
          <th colspan="2">R3</th>
        </tr>
        <!-- 第二行表头 -->
        <tr>
          <th>R31</th>
          <th>R32</th>
        </tr>
      </thead>
      <!-- 表体 -->
      <tbody>
        <tr v-for="(row, rowIndex) in processedTableData" :key="rowIndex">
          <td
            v-for="(cell, cellIndex) in row"
            :key="cellIndex"
            :colspan="cell.colspan"
            :rowspan="cell.rowspan"
            v-show="!cell.hidden"
          >
            {{ cell.value }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'

interface Props {
  code: string
}

const props = defineProps<Props>()

const loading = ref(false)

// 示例数据
const tableData = ref([
  { a: 'a3', b: 'b3', c: 'c3', d: 'c3', e: 'e3' },
  { a: 'a3', b: 'b3', c: 'c3', d: 'c3', e: 'e4' },
  { a: 'a3', b: 'a5', c: 'c5', d: 'd5', e: 'e5' },
  { a: 'a3', b: 'b6', c: 'c6', d: 'd6', e: 'd6' },
  { a: 'a7', b: 'b6', c: 'c7', d: 'd7', e: 'e7' },
  { a: 'a8', b: 'a8', c: 'c8', d: 'd8', e: 'd8' }
])

const columns = ['a', 'b', 'c', 'd', 'e']

// 处理表格数据，应用合并规则
const processedTableData = computed(() => {
  if (!tableData.value.length) return []
  
  const result = []
  
  // 初始化数据结构
  tableData.value.forEach((row, rowIndex) => {
    const processedRow = []
    columns.forEach((column) => {
      processedRow.push({
        value: row[column],
        colspan: 1,
        rowspan: 1,
        hidden: false,
        prop: column
      })
    })
    result.push(processedRow)
  })
  
  // 应用行合并规则
  applyRowMerge(result, 'a', null)
  applyRowMerge(result, 'b', 'a')
  applyRowMerge(result, 'c', 'b')
  applyRowMerge(result, 'd', 'c')
  
  // 应用列合并规则
  applyColMerge(result, ['a', 'b'])
  applyColMerge(result, ['c', 'd'])
  
  return result
})

// 应用行合并
const applyRowMerge = (data, column, relateColumn) => {
  const colIndex = columns.findIndex(col => col === column)
  const relateColIndex = relateColumn ? columns.findIndex(col => col === relateColumn) : -1
  
  if (colIndex === -1) return
  
  let startRow = 0
  
  while (startRow < data.length) {
    const startValue = data[startRow][colIndex].value
    const startRelateValue = relateColIndex >= 0 ? data[startRow][relateColIndex].value : null
    
    // 跳过空值
    if (!startValue) {
      startRow++
      continue
    }
    
    // 检查关联列条件
    if (relateColumn && !startRelateValue) {
      startRow++
      continue
    }
    
    let endRow = startRow
    
    // 查找连续相同的值
    for (let i = startRow + 1; i < data.length; i++) {
      const currentValue = data[i][colIndex].value
      const currentRelateValue = relateColIndex >= 0 ? data[i][relateColIndex].value : null
      
      if (currentValue === startValue && 
          (!relateColumn || currentRelateValue === startRelateValue)) {
        endRow = i
      } else {
        break
      }
    }
    
    // 如果有合并的行
    if (endRow > startRow) {
      data[startRow][colIndex].rowspan = endRow - startRow + 1
      
      // 隐藏被合并的单元格
      for (let i = startRow + 1; i <= endRow; i++) {
        data[i][colIndex].hidden = true
      }
    }
    
    startRow = endRow + 1
  }
}

// 应用列合并
const applyColMerge = (data, columnNames) => {
  const colIndexes = columnNames.map(col => columns.findIndex(c => c === col)).filter(idx => idx >= 0)
  
  if (colIndexes.length < 2) return
  
  data.forEach(row => {
    let startCol = 0
    
    while (startCol < colIndexes.length) {
      const startColIndex = colIndexes[startCol]
      const startValue = row[startColIndex].value
      
      // 跳过空值或已隐藏的单元格
      if (!startValue || row[startColIndex].hidden) {
        startCol++
        continue
      }
      
      let endCol = startCol
      
      // 查找连续相同的值
      for (let i = startCol + 1; i < colIndexes.length; i++) {
        const currentColIndex = colIndexes[i]
        const currentValue = row[currentColIndex].value
        
        if (currentValue === startValue && !row[currentColIndex].hidden) {
          endCol = i
        } else {
          break
        }
      }
      
      // 如果有合并的列
      if (endCol > startCol) {
        row[startColIndex].colspan = endCol - startCol + 1
        
        // 隐藏被合并的单元格
        for (let i = startCol + 1; i <= endCol; i++) {
          const colIndex = colIndexes[i]
          row[colIndex].hidden = true
        }
      }
      
      startCol = endCol + 1
    }
  })
}

onMounted(() => {
  console.log('BasicTable mounted with code:', props.code)
})
</script>

<style scoped>
.basic-table {
  width: 100%;
  overflow-x: auto;
}

.custom-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.custom-table th,
.custom-table td {
  border: 1px solid #dcdfe6;
  text-align: center;
  vertical-align: middle;
  min-width: 80px;
  padding: 8px 12px;
}

.custom-table th {
  background-color: #f5f7fa;
  font-weight: bold;
  color: #606266;
}

.custom-table td {
  background-color: #ffffff;
  color: #606266;
}

.custom-table tr:nth-child(even) td {
  background-color: #fafafa;
}

.custom-table tr:hover td {
  background-color: #f5f7fa;
}
</style>
