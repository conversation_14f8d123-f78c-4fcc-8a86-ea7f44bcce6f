<template>
  <div class="default-main page p-6 bg-white rounded shadow">
    <div class="mb-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="font-bold text-2xl text-gray-800">数据分析-提升效率</h2>
        <div class="flex space-x-2">
          <el-button class="!px-4" icon="DataAnalysis">数据分析</el-button>
          <el-button class="!px-4" type="primary" icon="Setting" @click="goDataManagement">数据管理</el-button>
        </div>
      </div>

      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex flex-wrap items-center gap-2">
          <el-select 
            v-model="chartType" 
            placeholder="选择图表类型" 
            style="width: 150px"
            class="mr-4"
          >
            <el-option label="提升效率" value="04a" />
            <el-option label="提升机运行率" value="04b" />
          </el-select>
          <ChartDateRange1 v-model="chartDateRange" :allow-view-types="['daily', 'weekly', 'monthly']" v-show="chartType !== '04b'" />
          <ChartDate1 v-model="chartDate" v-show="chartType === '04b'" />
        </div>
      </div>
    </div>

    <div class="w-full">
      <Hoisting04a 
        v-if="chartType === '04a'" 
        :date-range="chartDateRange" 
      />
      <Hoisting04b 
        v-if="chartType === '04b'" 
        :date="chartDate" 
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChartDateRange1 from '@/components/ChartDateRange/ChartDateRange1.vue'
import ChartDate1 from '@/components/ChartDate/ChartDate1.vue'
import Hoisting04a from './components/Hoisting04a.vue'
import Hoisting04b from './components/Hoisting04b.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const goDataManagement = () => {
  router.push('/demo/actual_hoist_data')
}

// 图表类型选择
const chartType = ref('04a')

// 日期范围
const chartDateRange = ref({
  autoLoad: true,
  viewType: 'daily',
  startDate: '',
  endDate: '',
})

const chartDate = ref({
  viewType: 'daily',
  date: '',
})
</script>
