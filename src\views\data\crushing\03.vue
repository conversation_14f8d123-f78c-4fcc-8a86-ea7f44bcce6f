<template>
  <div class="default-main page p-6 bg-white rounded shadow">
    <div class="mb-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="font-bold text-2xl text-gray-800">数据分析-故障分析</h2>
        <div class="flex space-x-2">
          <el-button class="!px-4" icon="DataAnalysis">数据分析</el-button>
          <el-button class="!px-4" type="primary" icon="Setting" @click="goDataManagement">数据管理</el-button>
        </div>
      </div>

      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex flex-wrap items-center gap-2">
          <!-- <el-select 
            v-model="chartType" 
            placeholder="选择图表类型" 
            style="width: 150px"
            class="mr-4"
          >
            <el-option label="故障分析" value="03a" />
          </el-select> -->
          <ChartDateRange1 
            v-model="chartDateRange" 
            :allow-view-types="['monthly', 'yearly']" 
            :default-date-range="{
              viewType: 'monthly',
              startDate: '2025-01-01',
              endDate: '2025-12-31',
            }" 
          />
        </div>
      </div>
    </div>

    <div class="w-full">
      <Crushing03a 
        v-if="chartType === '03a'" 
        :date-range="chartDateRange" 
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { dayjs } from 'element-plus'
import ChartDateRange1 from '@/components/ChartDateRange/ChartDateRange1.vue'
import Crushing03a from './components/Crushing03a.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const goDataManagement = () => {
  router.push('/crushing/data')
}

// 图表类型选择
const chartType = ref('03a')

// 日期范围
const chartDateRange = ref({
  autoLoad: true,
  viewType: 'monthly', // daily 或 monthly
  startDate: dayjs().startOf('year').format('YYYY-MM-DD'),
  endDate: dayjs().endOf('month').format('YYYY-MM-DD'),
})
</script>
