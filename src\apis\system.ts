// 自动生成的 API 文件，请勿手动修改

import { request } from '@/utils/hsj/service/index'

export interface TSysLayoutConfig {
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  params?: any;
  id?: number;
  layoutName?: string;
  layoutType?: string;
  configJson?: string;
}

export interface TPageResultSysLayoutConfig {
  total?: number;
  rows?: TSysLayoutConfig[];
}

export async function apiSystemLayoutUpdate(
  data: TSysLayoutConfig
): Promise<number> {
  return request<{ data: number }>({
    url: '/system/layout',
    method: 'put',
    data,
  }).then((res) => res.data)
}

export async function apiSystemLayoutCreate(
  data: TSysLayoutConfig
): Promise<number> {
  return request<{ data: number }>({
    url: '/system/layout',
    method: 'post',
    data,
  }).then((res) => res.data)
}

export async function apiSystemLayoutId(id: number): Promise<TSysLayoutConfig> {
  return request<{ data: TSysLayoutConfig }>({
    url: `/system/layout/${id}`,
    method: 'get',
  }).then((res) => res.data)
}

export async function apiSystemLayoutList(
  query: TSysLayoutConfig
): Promise<TPageResultSysLayoutConfig> {
  return request<{ data: TPageResultSysLayoutConfig }>({
    url: '/system/layout/list',
    method: 'get',
    params: query,
  }).then((res) => res.data)
}
