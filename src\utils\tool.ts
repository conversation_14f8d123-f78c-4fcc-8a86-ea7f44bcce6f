import dayjs from 'dayjs'
import Mock from 'tony-mockjs'

export class ToolUtils {
  static sleep(time: number) {
    return new Promise((resolve) => {
      setTimeout(resolve, time)
    })
  }

  static random = Mock.Random

  static formatDisplayDate(viewType: string, item: {
    operationDate?: string,
    year?: number,
    weekNumber?: number,
    month?: number,
  }) {
    if (viewType === 'daily') {
      return dayjs(item.operationDate, 'YYYY-MM-DD').format('MM-DD')
    } else if (viewType === 'weekly') {
      return `${item.year}年${item.weekNumber}周`
    } else if (viewType === 'monthly') {
      return item.year && item.month ? `${item.year}年${item.month}月` : ''
    } else if (viewType === 'yearly') {
      return item.year ? `${item.year}年` : ''
    }
    return ''
  }
}