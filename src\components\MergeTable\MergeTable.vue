<template>
  <div>
    <div class="text-center text-xl font-bold py-4">
      {{ title }} ({{ formatDate }})
    </div>
    <CommonTable v-for="table in tables" :key="table.id" v-bind="table.props" />
  </div>
</template>

<script setup lang="ts">
import { apiSystemLayoutId } from '@/apis/system'
import dayjs from 'dayjs'
import JSON5 from 'json5'
import { ref, computed, watch } from 'vue'

const props = defineProps<{
  id: number
  viewType: 'daily' | 'weekly' | 'monthly' | 'yearly'
  date: string
}>()

const title = ref('')
// const date = ref(dayjs().format('YYYY-MM-DD'))

const formatDate = computed(() => {
  if (props.viewType === 'daily')
    return dayjs(props.date).format('YYYY年MM月DD日')
  if (props.viewType === 'weekly')
    return `${dayjs(props.date).startOf('week').format('YYYY年MM月DD日')} - ${dayjs(props.date).endOf('week').format('YYYY年MM月DD日')}`
  if (props.viewType === 'monthly') {
    return dayjs(props.date).format('YYYY年MM月')
  }
  if (props.viewType === 'yearly') return dayjs(props.date).format('YYYY年')
  return ''
})

const widgetList = ref<
  {
    id: string
    x: number
    y: number
    w: number
    h: number
    component: string
    title: string
    props: {
      params: {
        date: any // 是否需要日期
      }
      code: string // 输入代码
      hideHeader: boolean // 渲染时是否隐藏表头
    }
  }[]
>([])

const tables = computed(() => {
  const list = widgetList.value
    .filter((item) => item.component === 'CommonTable')
    .map((item) => {
      const newProps = {
        ...item.props,
        params: {
          ...item.props.params,
          ...(item.props.params.date
            ? { date: dayjs(props.date).format('YYYY-MM-DD') }
            : {}),
        },
      }
      return {
        id: item.id,
        props: newProps,
      }
    })
  return list
})

watch(
  () => props.id,
  async () => {
    widgetList.value = []
    const res = await apiSystemLayoutId(props.id)
    const config = JSON5.parse(res.configJson || '{}')
    title.value = res.layoutName || ''
    widgetList.value = config.widgets
  },
  {
    immediate: true,
  }
)
</script>
