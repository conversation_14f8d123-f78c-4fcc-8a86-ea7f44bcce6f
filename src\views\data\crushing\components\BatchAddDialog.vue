<script setup lang="ts">
import { dataBaseUrl } from '@/api/config/base.js'
import { apiDataCrushingBatch } from '@/apis/data'
import { request } from '@/utils/hsj/service/index'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'

const pageName = 'crushing'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  periodOptions: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const selectedDate = ref('')

const tableData = ref<any[]>([])
const loading = ref(false)

const formRef = ref<any>(null)
const rules = {
  selectedDate: [{ required: true, message: '请选择日期', trigger: 'change' }],
}

const fetchData = async () => {
  if (!selectedDate.value) {
    ElMessage.warning('请先选择日期')
    return
  }

  loading.value = true
  try {
    const res = await request<any>({
      url: `${dataBaseUrl}/${pageName}/list`,
      method: 'get',
      params: {
        operationDate: selectedDate.value,
        pageSize: 999,
        orderByColumn: 'workingPeriodId',
        orderByType: 'asc',
      },
    })

    if (res.code === 200) {
      tableData.value =
        res.rows.map((item: any) => {
          return {
            ...item,
            workingPeriodId: String(item.workingPeriodId),
          }
        }) || []
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取破碎数据失败:', error)
    ElMessage.error('获取破碎数据失败')
  } finally {
    loading.value = false
  }
}

const addRow = () => {
  if (!selectedDate.value) {
    ElMessage.warning('请先选择日期')
    return
  }

  tableData.value.push({
    operationDate: selectedDate.value,
    workingPeriodId: '',
    operationTime: '',
    faultTime: '',
    faultStartTime: '',
    faultEndTime: '',
    faultReason: '',
    crushingVolume: '',
    isNew: true,
  })

  setTimeout(() => {
    const selectElements = document.querySelectorAll(
      '.batch-add-dialog .el-table .el-select'
    )
    if (selectElements && selectElements.length > 0) {
      const targetSelect = selectElements[selectElements.length - 1]
      if (targetSelect) {
        const input = targetSelect.querySelector('input')
        if (input) {
          ;(input as HTMLElement).focus()
        } else {
          ;(targetSelect as HTMLElement).click()
        }
      }
    }
  }, 100)
}

const deleteRow = (index: number, _row: any) => {
  tableData.value.splice(index, 1)
}

const handleDialogClosed = () => {
  // Clear form data when dialog is closed
  selectedDate.value = ''
  tableData.value = []
}

const saveAll = async () => {
  if (!selectedDate.value) {
    ElMessage.warning('请先选择日期')
    return
  }

  for (const item of tableData.value) {
    if (!item.workingPeriodId) {
      ElMessage.warning('请选择作业时段')
      return
    }
    if (
      item.operationTime === undefined ||
      item.operationTime === null ||
      item.operationTime === ''
    ) {
      ElMessage.warning('请输入运行时间')
      return
    }
    if (
      item.crushingVolume === undefined ||
      item.crushingVolume === null ||
      item.crushingVolume === ''
    ) {
      ElMessage.warning('请输入破碎量')
      return
    }
  }

  console.log('提交数据:', JSON.stringify(tableData.value, null, 2))

  loading.value = true
  try {
    // Prepare data for submission
    const submitData = tableData.value.map((item) => ({
      ...item,
      operationDate: selectedDate.value,
    }))

    // Call the API to save the data
    const res = await apiDataCrushingBatch(submitData)

    if (res) {
      ElMessage.success('保存成功')
      dialogVisible.value = false
      emit('refresh')
    } else {
      ElMessage.error('保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

watch(selectedDate, (newVal) => {
  if (newVal) {
    tableData.value = []
    fetchData()
  }
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="按日添加破碎数据"
    destroy-on-close
    width="1200px"
    :close-on-click-modal="false"
    append-to-body
    class="batch-add-dialog"
    @closed="handleDialogClosed"
  >
    <el-form
      ref="formRef"
      :model="{ selectedDate }"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="选择日期" prop="selectedDate">
        <el-date-picker
          v-model="selectedDate"
          type="date"
          placeholder="请选择日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="success" @click="addRow">
          <el-icon><component :is="Plus" /></el-icon> 添加行
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      max-height="600px"
    >
      <el-table-column label="作业时段" prop="workingPeriodId">
        <template #default="{ row }">
          <el-select
            v-model="row.workingPeriodId"
            placeholder="请选择作业时段"
            style="width: 100%"
          >
            <el-option
              v-for="item in periodOptions"
              :key="(item as any).value"
              :label="(item as any).label"
              :value="(item as any).value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column label="运行时间" prop="operationTime">
        <template #default="{ row }">
          <el-input v-model="row.operationTime" placeholder="请输入运行时间" />
        </template>
      </el-table-column>

      <el-table-column label="破碎量" prop="crushingVolume">
        <template #default="{ row }">
          <el-input v-model="row.crushingVolume" placeholder="请输入破碎量" />
        </template>
      </el-table-column>

      <el-table-column label="故障时长" prop="faultTime">
        <template #default="{ row }">
          <el-input v-model="row.faultTime" placeholder="请输入故障时长" />
        </template>
      </el-table-column>

      <el-table-column label="故障原因" prop="faultReason">
        <template #default="{ row }">
          <el-input v-model="row.faultReason" placeholder="请输入故障原因" />
        </template>
      </el-table-column>

      <el-table-column label="故障开始时间" prop="faultStartTime">
        <template #default="{ row }">
          <el-time-picker
            v-model="row.faultStartTime"
            placeholder="请选择故障开始时间"
            format="HH:mm"
            value-format="HH:mm:ss"
            style="width: 100%"
          />
        </template>
      </el-table-column>

      <el-table-column label="故障结束时间" prop="faultEndTime">
        <template #default="{ row }">
          <el-time-picker
            v-model="row.faultEndTime"
            placeholder="请选择故障结束时间"
            format="HH:mm"
            value-format="HH:mm:ss"
            style="width: 100%"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="{ row, $index }">
          <el-button type="danger" link @click="deleteRow($index, row)">
            <el-icon><component :is="Delete" /></el-icon> 删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveAll" :loading="loading"
        >保 存</el-button
      >
    </template>
  </el-dialog>
</template>

<style>
.batch-add-dialog .el-dialog__body {
  padding: 20px !important;
}

.batch-add-dialog .el-table {
  margin-top: 15px;
  width: 100% !important;
}

.batch-add-dialog .el-dialog__header,
.batch-add-dialog .el-dialog__footer {
  padding: 15px 20px !important;
}

.batch-add-dialog .el-dialog__body .el-table__body-wrapper {
  overflow-y: auto;
}

.batch-add-dialog .el-form-item {
  margin-bottom: 18px;
}
</style>
