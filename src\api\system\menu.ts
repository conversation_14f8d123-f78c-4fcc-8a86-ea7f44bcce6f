import { request } from '@/utils/hsj/service/index'

// 查询菜单列表
export function listMenu(query?: anyObj) {
  return request({
    url: '/system/menu/list',
    method: 'get',
    params: query,
  })
}

// 查询菜单下拉树结构
export function treeselect() {
  return request({
    url: '/system/menu/treeselect',
    method: 'get',
  })
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId: number) {
  return request({
    url: '/system/menu/roleMenuTreeselect/' + roleId,
    method: 'get',
  })
}
