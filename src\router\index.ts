import { createWebH<PERSON><PERSON>, createRouter, RouteRecordRaw } from 'vue-router'
import { beforeEach, afterEach } from './routerInterceptor'
import Layout from '@/layout/index.vue'

// 公共路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue'),
      },
    ],
  },
  {
    path: '/test',
    component: Layout,
    children: [
      {
        path: 'excel-import',
        component: () => import('@/views/test/excel-import/index.vue'),
        name: 'ExcelImportTest',
        meta: { title: 'Excel导入测试', icon: 'excel' },
      },
      // {
      //   path: 'common-table',
      //   component: () => import('@/views/test/CommonTableTest.vue'),
      //   name: 'CommonTableTest',
      //   meta: { title: 'CommonTable测试', icon: 'table' },
      // },
      // {
      //   path: 'simple-table',
      //   component: () => import('@/views/test/SimpleTableTest.vue'),
      //   name: 'SimpleTableTest',
      //   meta: { title: '简化表格测试', icon: 'table' },
      // },
      // {
      //   path: 'basic-table',
      //   component: () => import('@/views/test/BasicTableTest.vue'),
      //   name: 'BasicTableTest',
      //   meta: { title: '基础表格测试', icon: 'table' },
      // },
      // {
      //   path: 'final-table',
      //   component: () => import('@/views/test/FinalTableTest.vue'),
      //   name: 'FinalTableTest',
      //   meta: { title: '完整功能演示', icon: 'table' },
      // },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404.vue'),
    hidden: true,
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/index.vue'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true },
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login/login.vue'),
    hidden: true,
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index.vue'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' },
      },
    ],
  },
  
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes: RouteRecordRaw[] = [
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser.vue'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' },
      },
    ],
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data.vue'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' },
      },
    ],
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log.vue'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' },
      },
    ],
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable.vue'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' },
      },
    ],
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(_to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

beforeEach(router)
afterEach(router)
export default router
