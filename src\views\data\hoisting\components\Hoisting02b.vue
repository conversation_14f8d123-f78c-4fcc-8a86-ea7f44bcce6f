<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsHoisting02b } from '@/apis/data'
import type { TActualHoistPeriodStats } from '@/apis/model'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TActualHoistPeriodStats[]>([])
const lastViewType = ref('daily')

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate,
    }

    const res = await apiDataStatsHoisting02b(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取运行时间数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 计算每个时段的总时间（分钟）
const totalMinutes = computed(() => {
  return {
    daily: 24 * 60 / 3,
    weekly: 7 * 24 * 60 / 3,
    monthly: 30 * 24 * 60 / 3,
  }[props.dateRange.viewType] || 0
})

// 按日期和时段分组数据
const groupedData = computed(() => {
  const groups: Record<string, Record<string, { 
    operationTime?: number; 
    faultTime?: number;
    totalTime?: number;
  }>> = {}

  chartData.value.forEach((item: TActualHoistPeriodStats) => {
    const dateKey = formatDisplayDate(item)
    if (!dateKey) return

    if (!groups[dateKey]) {
      groups[dateKey] = {}
    }

    const period = item.workingPeriodName || '未知时段'
    // 每个时段8小时 = 480分钟
    const periodMinutes = 8 * 60
    
    groups[dateKey][period] = {
      operationTime: item.totalOperationTime || 0,
      faultTime: item.totalFaultTime || 0,
      totalTime: periodMinutes,
    }
  })

  return groups
})

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) return undefined

  // 获取所有唯一的日期
  const dates = Object.keys(groupedData.value).sort()

  // 获取所有时段
  const periods = Array.from(
    new Set(
      Object.values(groupedData.value).flatMap((periods) =>
        Object.keys(periods)
      )
    )
  ).sort((a, b) => {
    // 按时段名称排序：0-8, 8-20, 20-0
    const startA = Number(a.split('-')[0])
    const startB = Number(b.split('-')[0])
    return startA - startB
  })

  const series = periods.map((period) => ({
    name: period,
    type: 'bar',
    stack: 'total',
    barWidth: '60%',
    emphasis: {
      focus: 'series',
    },
    data: dates.map(
      (date) => groupedData.value[date]?.[period]?.faultTime || 0
    ),
    itemStyle: {
      color:
        period === '0-8时'
          ? '#409EFF'
          : period === '8-20时'
            ? '#36CFC9'
            : '#95DE64',
    },
    label: {
      show: true,
      position: 'inside',
      formatter: (params: any) => {
        return params.value > 0 ? params.value : ''
      },
      color: '#fff',
      fontSize: 10,
      fontWeight: 'bold',
    },
  }))

  // 添加总量系列（不显示在图例中）
  const totalSeries = {
    name: '总量',
    type: 'bar',
    stack: 'total',
    barWidth: '60%',
    legendHoverLink: false, // 禁用图例悬停交互
    silent: true, // 禁用交互
    label: {
      show: true,
      position: 'top',
      distance: 10,
      formatter: (params: any) => {
        const total = periods.reduce((sum, period) => {
          return (
            sum +
            (groupedData.value[dates[params.dataIndex]]?.[period]?.faultTime ||
              0)
          )
        }, 0)
        return total > 0 ? total : ''
      },
      color: '#333',
      fontSize: 12,
      fontWeight: 'bold',
    },
    itemStyle: {
      color: 'transparent',
    },
    data: dates.map(() => 0),
  }

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any[]) => {
        const date = params[0].axisValue
        const total = params
          .filter((p) => p.seriesName !== '总量')
          .reduce((sum, item) => sum + item.value, 0)

        let result = `${date}<br/>`

        params.forEach((item) => {
          if (item.seriesName !== '总量') {
            const percent =
              total > 0 ? ((item.value / total) * 100).toFixed(1) : '0.0'
            result += `${item.marker} ${item.seriesName}: ${item.value}分钟 (${percent}%)<br/>`
          }
        })

        result += `<span style="font-weight:bold">总计: ${total}分钟</span>`
        return result
      },
    },
    legend: {
      data: periods, // 只显示时段数据，不显示总量
      bottom: 0,
      left: 'center',
      itemGap: 20,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 12,
        lineHeight: 16,
      },
    },
    grid: {
      left: '3%',
      right: '3%',
      top: '10%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: ['5%', '5%'],
      data: dates,
      axisLabel: {
        rotate: 0,
        margin: 15,
        overflow: 'truncate',
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: 'value',
      name: '故障处理时间(分钟)',
      min: 0,
      axisLabel: {
        formatter: '{value}',
      },
    },
    series: [...series, totalSeries],
  }
})

</script>
