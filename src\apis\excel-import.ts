import { request, blob } from '@/utils/hsj/service/index'
import { AxiosResponse } from 'axios'


export interface TExcelFieldInfo {
  fieldName: string;
  displayName: string;
  fieldType: string;
  required: boolean;
  sort: number;
  hasOptions: boolean;
}

export interface TExcelOptionInfo {
  value: string;
  label: string;
}

export interface TExcelTemplateInfo {
  key?: string;
  name?: string;
  description?: string;
  sheetName?: string;
  titleRows?: number;
  maxRows?: number;
  fields?: TExcelFieldInfo[];
  fieldOptions?: any;
}

export interface TExcelDataInfoObject {
  row?: number;
  data?: any;
  type?: string;
  messages?: TMessage[];
}

export interface TExcelImportResultObject {
  id?: string;
  total?: number;
  success?: number;
  error?: number;
  warning?: number;
  dataList?: TExcelDataInfoObject[];
  message?: string;
}

export interface TMessage {
  type?: string;
  column?: string;
  message?: string;
}
/**
 * 获取所有模板信息
 */
export async function getAllTemplates(): Promise<Record<string, TExcelTemplateInfo>> {
  return request<{ data: Record<string, TExcelTemplateInfo> }>({
    url: '/common/excel/templates',
    method: 'get'
  }).then(res => res.data);
}

/**
 * 获取指定模板信息
 * @param key 模板key
 */
export async function getTemplateInfo(key: string): Promise<TExcelTemplateInfo> {
  return request<{ data: TExcelTemplateInfo }>({
    url: `/common/excel/template/${key}`,
    method: 'get'
  }).then(res => res.data);
}

/**
 * 下载Excel模板
 * @param key 模板key
 */
export async function downloadTemplate(key: string): Promise<AxiosResponse<Blob>> {
  return blob({
    url: `/common/excel/template/${key}/download`,
    method: 'get'
  });
}

/**
 * 验证Excel数据
 * @param key 模板key
 * @param data 包含文件的FormData
 */
export async function validateExcel(key: string, data: FormData): Promise<TExcelImportResultObject> {
  return request<{ data: TExcelImportResultObject }>({
    url: `/common/excel/validate/${key}`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).then(res => res.data);
}

/**
 * 验证单行数据
 * @param key 模板key
 * @param rowData 行数据
 */
export async function validateRow(key: string, rowData: TExcelDataInfoObject): Promise<TExcelDataInfoObject> {
  return request<{ data: TExcelDataInfoObject }>({
    url: `/common/excel/validate/${key}/row`,
    method: 'post',
    data: rowData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(res => res.data);
}

/**
 * 导入Excel数据（通过缓存ID）
 * @param key 模板key
 * @param cacheId 缓存ID
 * @param dataList 数据列表
 */
export async function importExcel<T>(
  key: string, 
  cacheId: string, 
  dataList: TExcelDataInfoObject[]
): Promise<TExcelImportResultObject> {
  return request<{ data: TExcelImportResultObject }>({
    url: `/common/excel/import/${key}/${cacheId}`,
    method: 'post',
    data: dataList
  }).then(res => res.data);
}

/**
 * 导入Excel文件（一步完成验证和导入）
 * @param key 模板key
 * @param data 包含文件的FormData
 */
export async function importExcelFile(key: string, data: FormData): Promise<TExcelImportResultObject> {
  return request<{ data: TExcelImportResultObject }>({
    url: `/common/excel/import/${key}/file`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).then(res => res.data);
}

/**
 * 下载失败数据
 * @param key 模板key
 * @param cacheId 缓存ID
 */
export async function downloadFailures(key: string, cacheId: string): Promise<AxiosResponse<Blob>> {
  return blob({
    url: `/common/excel/failures/${key}/${cacheId}`,
    method: 'get'
  });
}
