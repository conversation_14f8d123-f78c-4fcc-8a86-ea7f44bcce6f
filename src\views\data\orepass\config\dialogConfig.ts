export default (): BaseFormProps => {
  return {
    itemStyle: {
      padding: '0px 0px 0px 0px',
    },
    rules: {
      projectDepartmentId: [
        { required: true, message: '项目部不能为空', trigger: 'change' },
      ],
      orePassId: [
        { required: true, message: '溜井不能为空', trigger: 'change' },
      ],
      workingPeriodId: [
        { required: true, message: '作业时段不能为空', trigger: 'change' },
      ],
      operationDate: [
        { required: true, message: '作业日期不能为空', trigger: 'blur' },
      ],
    },
    formItems: [
      {
        field: 'operationDate',
        label: '作业日期',
        config: {
          clearable: false,
          type: 'date',
          disabledDate: (time: Date) => {
            return time.getTime() > Date.now()
          },
        },
        type: 'datepicker',
      },
      {
        field: 'projectDepartmentId',
        type: 'select',
        options: [],
        label: '项目部',
      },
      {
        field: 'workingPeriodId',
        type: 'select',
        options: [],
        label: '作业时段',
      },
      {
        field: 'orePassId',
        type: 'select',
        options: [],
        label: '溜井',
      },
      {
        field: 'trips',
        type: 'input',
        label: '溜放趟数',
      },
      {
        field: 'oreTons',
        type: 'input',
        label: '溜放矿石量',
      },
    ],
    colLayout: {
      xl: 24,
      lg: 24,
      md: 24,
      sm: 24,
      xs: 24,
    },
    elFormConfig: {
      labelWidth: '100px',
    },
  }
}
