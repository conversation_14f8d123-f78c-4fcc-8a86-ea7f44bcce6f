<template>
  <div class="excel-import-example">
    <h2>Excel导入组件使用示例</h2>
    
    <div class="example-section">
      <h3>基础用法</h3>
      <el-button type="primary" @click="openBasicImport">
        打开基础导入
      </el-button>
      
      <h3>自定义配置</h3>
      <el-button type="success" @click="openCustomImport">
        打开自定义导入
      </el-button>
      
      <h3>模板选择</h3>
      <el-select v-model="selectedTemplate" placeholder="选择导入模板">
        <el-option
          v-for="template in templateOptions"
          :key="template.key"
          :label="template.name"
          :value="template.key"
        />
      </el-select>
      <el-button 
        type="warning" 
        @click="openTemplateImport"
        :disabled="!selectedTemplate"
      >
        使用选中模板导入
      </el-button>
    </div>

    <!-- 基础导入弹窗 -->
    <ExcelStepImport
      v-model:visible="basicImportVisible"
      template-key="simple_test"
      title="基础Excel导入"
      @success="handleImportSuccess"
      @error="handleImportError"
      @close="handleImportClose"
    />

    <!-- 自定义配置导入弹窗 -->
    <ExcelStepImport
      v-model:visible="customImportVisible"
      :template-key="selectedTemplate"
      title="自定义Excel导入"
      width="1400px"
      :max-file-size="20"
      :accept-types="['.xlsx', '.xls', '.csv']"
      :auto-import="false"
      @success="handleImportSuccess"
      @error="handleImportError"
      @close="handleImportClose"
    />

    <!-- 模板导入弹窗 -->
    <ExcelStepImport
      v-model:visible="templateImportVisible"
      :template-key="selectedTemplate"
      :title="`${selectedTemplateName}数据导入`"
      @success="handleImportSuccess"
      @error="handleImportError"
      @close="handleImportClose"
    />

    <!-- 结果显示 -->
    <div v-if="lastResult" class="result-section">
      <h3>最后一次导入结果</h3>
      <el-card>
        <div class="result-stats">
          <div class="stat-item">
            <span class="label">总记录数:</span>
            <span class="value">{{ lastResult.total || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="label">成功数:</span>
            <span class="value success">{{ lastResult.success || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="label">失败数:</span>
            <span class="value error">{{ lastResult.error || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="label">警告数:</span>
            <span class="value warning">{{ lastResult.warning || 0 }}</span>
          </div>
        </div>
        <div v-if="lastResult.message" class="result-message">
          <strong>消息:</strong> {{ lastResult.message }}
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import ExcelStepImport from './index'
import type { TExcelImportResultObject } from '@/apis/model'

// 响应式数据
const basicImportVisible = ref(false)
const customImportVisible = ref(false)
const templateImportVisible = ref(false)
const selectedTemplate = ref('')
const lastResult = ref<TExcelImportResultObject | null>(null)

// 模板选项（实际使用时应该从API获取）
const templateOptions = ref([
  { key: 'simple_test', name: '简单测试导入' },
  { key: 'user_import', name: '用户数据导入' },
  { key: 'product_import', name: '产品数据导入' },
  { key: 'order_import', name: '订单数据导入' }
])

// 计算属性
const selectedTemplateName = computed(() => {
  const template = templateOptions.value.find(t => t.key === selectedTemplate.value)
  return template?.name || ''
})

// 方法
const openBasicImport = () => {
  basicImportVisible.value = true
}

const openCustomImport = () => {
  if (!selectedTemplate.value) {
    ElMessage.warning('请先选择一个模板')
    return
  }
  customImportVisible.value = true
}

const openTemplateImport = () => {
  if (!selectedTemplate.value) {
    ElMessage.warning('请先选择一个模板')
    return
  }
  templateImportVisible.value = true
}

const handleImportSuccess = (result: TExcelImportResultObject) => {
  console.log('导入成功:', result)
  lastResult.value = result
  ElMessage.success(`导入成功！成功导入 ${result.success || 0} 条记录`)
}

const handleImportError = (error: any) => {
  console.error('导入失败:', error)
  ElMessage.error('导入失败，请检查数据格式')
}

const handleImportClose = () => {
  console.log('导入弹窗关闭')
}
</script>

<style scoped>
.excel-import-example {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #409eff;
    margin-bottom: 2rem;
    text-align: center;
  }

  .example-section {
    margin-bottom: 3rem;

    h3 {
      color: #606266;
      margin: 1.5rem 0 1rem 0;
      border-left: 4px solid #409eff;
      padding-left: 1rem;
    }

    .el-button {
      margin-right: 1rem;
      margin-bottom: 1rem;
    }

    .el-select {
      width: 200px;
      margin-right: 1rem;
    }
  }

  .result-section {
    margin-top: 2rem;

    h3 {
      color: #606266;
      margin-bottom: 1rem;
    }

    .result-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
      margin-bottom: 1rem;

      .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem;
        background: #f5f7fa;
        border-radius: 4px;

        .label {
          font-weight: 500;
          color: #606266;
        }

        .value {
          font-weight: bold;

          &.success {
            color: #67c23a;
          }

          &.error {
            color: #f56c6c;
          }

          &.warning {
            color: #e6a23c;
          }
        }
      }
    }

    .result-message {
      padding: 1rem;
      background: #f0f9ff;
      border-radius: 4px;
      color: #606266;
    }
  }
}
</style>
