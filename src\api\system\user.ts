import { request } from '@/utils/hsj/service/index'
import { parseStrEmpty } from '@/utils/ruoyi'

// 查询用户详细
export function getUser(userId?: StrNum) {
  return request({
    url: '/system/user/' + parseStrEmpty(userId),
    method: 'get',
  })
}

// 用户密码重置
export function resetUserPwd(userId: StrNum, password: string) {
  const data = {
    userId,
    password,
  }
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data: data,
  })
}

// 用户状态修改
export function changeUserStatus(userId: StrNum, status: StrNum) {
  const data = {
    userId,
    status,
  }
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data: data,
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request<UserProfileApi>({
    url: '/system/user/profile',
    method: 'get',
  })
}

// 修改用户个人信息
export function updateUserProfile(data: anyObj) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data,
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword: string, newPassword: string) {
  const data = {
    oldPassword,
    newPassword,
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    data: data,
  })
}

// 用户头像上传
export function uploadAvatar(data: anyObj) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: data,
  })
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: '/system/user/deptTree',
    method: 'get',
  })
}
