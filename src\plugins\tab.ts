import useTagsViewStore from '@/store/modules/tagsView.ts'
import router from '@/router/index'

export default {
  // 刷新当前tab页签
  refreshPage(obj?: TagsType) {
    const { path, query, matched } = router.currentRoute.value
    if (obj === undefined) {
      matched.forEach((m) => {
        if (m.components && m.components.default && m.components.default.name) {
          if (!['Layout', 'ParentView'].includes(m.components.default.name)) {
            obj = { name: m.components.default.name, path: path, query: query }
          }
        }
      })
    }
    return useTagsViewStore()
      .delCachedView(obj!)
      .then(() => {
        const { path, query } = obj!
        router.replace({
          path: '/redirect' + path,
          query: query,
        })
      })
  },
  // 关闭当前tab页签，打开新页签
  closeOpenPage(obj: TagsType) {
    useTagsViewStore().delView(router.currentRoute.value)
    if (obj !== undefined) {
      return router.push(obj)
    }
  },
  // 关闭指定tab页签
  closePage(obj?: TagsType): Promise<{
    visitedViews: TagsType[]
    cachedViews: string[]
  }> {
    return new Promise((resolve) => {
      if (!obj) {
        useTagsViewStore()
          .delView(router.currentRoute.value)
          .then(({ visitedViews }) => {
            const latestView = visitedViews.slice(-1)[0]
            if (latestView) {
              router.push(latestView.fullPath!)
            }
            router.push('/')
          })
        resolve({
          visitedViews: [] as TagsType[],
          cachedViews: [] as string[],
        })
      }
      useTagsViewStore()
        .delView(obj!)
        .then((res) => {
          resolve(res)
        })
    })
  },
  // 关闭所有tab页签
  closeAllPage() {
    return useTagsViewStore().delAllViews()
  },
  // 关闭左侧tab页签
  closeLeftPage(obj: TagsType) {
    return useTagsViewStore().delLeftTags(obj || router.currentRoute.value)
  },
  // 关闭右侧tab页签
  closeRightPage(obj: TagsType) {
    return useTagsViewStore().delRightTags(obj || router.currentRoute.value)
  },
  // 关闭其他tab页签
  closeOtherPage(obj: TagsType) {
    return useTagsViewStore().delOthersViews(obj || router.currentRoute.value)
  },
  // 打开tab页签
  openPage(url: string) {
    return router.push(url)
  },
  // 修改tab页签
  updatePage(obj: TagsType) {
    return useTagsViewStore().updateVisitedView(obj)
  },
}
