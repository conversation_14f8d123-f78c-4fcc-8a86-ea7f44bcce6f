export const tableItem: BaseTableItem[] = [
  {
    prop: 'operationDate',
    label: '作业日期'
  },
  {
    prop: 'workingPeriodName',
    label: '作业时段'
  },
  {
    prop: 'operationTime',
    label: '运行时间'
  },
  {
    prop: 'faultTime',
    label: '故障时长'
  },
  {
    prop: 'crushingVolume',
    label: '破碎量'
  },
  {
    prop: 'faultReason',
    label: '故障原因'
  },
  {
    prop: 'faultStartTime',
    label: '故障开始时间'
  },
  {
    prop: 'faultEndTime',
    label: '故障结束时间'
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },
]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
