export default (): BaseFormProps => {
  return {
    itemStyle: {
      padding: '0px 0px 0px 0px',
    },
    rules: {},
    formItems: [
      {
        field: 'planDate',
        config: {
          clearable: false,
          type: 'month',
          valueFormat: 'YYYYMM',
          format: 'YYYY-MM',
        },
        type: 'datepicker',
        label: '计划月份',
      },
      {
        field: 'projectDepartmentId',
        type: 'select',
        options: [],
        label: '项目部',
      },
      {
        field: 'workingFaceId',
        type: 'select',
        options: [],
        label: '工作面',
      },
      {
        field: 'stopeId',
        type: 'select',
        options: [],
        label: '采场',
      },
      {
        field: 'boltMeshSupportMeter',
        type: 'input',
        label: '锚网支护米数',
      },
      {
        field: 'boltMeshSupportVolume',
        type: 'input',
        label: '锚网支护方量',
      },
      {
        field: 'shotcreteSupportMeter',
        type: 'input',
        label: '喷浆支护米数',
      },
      {
        field: 'shotcreteSupportVolume',
        type: 'input',
        label: '喷浆支护方量',
      },
      {
        field: 'supportVolume',
        type: 'input',
        label: '支护方量',
      },
      {
        field: 'supportMeter',
        type: 'input',
        label: '支护米数',
      },
    ],
    colLayout: {
      xl: 24,
      lg: 24,
      md: 24,
      sm: 24,
      xs: 24,
    },
    elFormConfig: {
      labelWidth: '100px',
    },
  }
}
