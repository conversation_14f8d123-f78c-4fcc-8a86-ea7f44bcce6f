export const tableItem: BaseTableItem[] = [
  {
    prop: 'operationDate',
    label: '作业日期',
  },
  {
    prop: 'projectDepartmentName',
    label: '项目部',
  },
  {
    prop: 'workingFaceName',
    label: '工作面',
  },
  {
    prop: 'workingPeriodName',
    label: '作业时段',
  },
  {
    prop: 'stopeName',
    label: '采场',
  },
  {
    prop: 'progressMeters',
    label: '进尺米数',
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },
]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
