# ExcelStepImport 通用Excel导入组件

基于prototype.html设计和excel-import.ts接口的通用三步Excel导入组件，支持文件上传、数据校验和导入执行。

## 功能特性

- 🚀 **三步导入流程**: 文件上传 → 数据校验 → 导入执行
- 📁 **拖拽上传**: 支持拖拽和点击上传Excel文件
- ✅ **数据校验**: 实时校验数据格式和必填字段
- ✏️ **在线编辑**: 支持在预览表格中直接编辑错误数据
- 🔽 **下拉选择**: 支持字段下拉选项，可筛选选择，不允许直接输入
- 🔄 **实时验证**: 编辑数据后自动调用单行验证API，实时更新错误状态
- 🚦 **智能提交**: 严格模式下只有无错误时才能提交，强制模式下可导入有效数据
- ⚡ **无动画编辑**: 去除编辑切换动画，提供更流畅的编辑体验
- 🔒 **流程控制**: 导入完成后禁用返回操作，确保流程完整性
- 📊 **进度显示**: 实时显示导入进度和状态
- 📥 **模板下载**: 支持下载Excel导入模板
- 🔄 **错误处理**: 完善的错误提示和失败数据下载
- 🎨 **美观界面**: 基于Element Plus的现代化UI设计

## 基础用法

```vue
<template>
  <div>
    <el-button @click="showImport = true">导入数据</el-button>
    
    <ExcelStepImport
      v-model:visible="showImport"
      template-key="your_template_key"
      title="数据导入"
      @success="handleSuccess"
      @error="handleError"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ExcelStepImport from '@/components/ExcelStepImport'

const showImport = ref(false)

const handleSuccess = (result) => {
  console.log('导入成功:', result)
}

const handleError = (error) => {
  console.error('导入失败:', error)
}
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | boolean | false | 是否显示弹窗 |
| templateKey | string | - | 模板key（必填） |
| title | string | 'Excel数据导入' | 弹窗标题 |
| width | string/number | '1200px' | 弹窗宽度 |
| closable | boolean | true | 是否可以关闭弹窗 |
| modal | boolean | true | 是否显示遮罩层 |
| maxFileSize | number | 10 | 最大文件大小(MB) |
| acceptTypes | string[] | ['.xlsx', '.xls'] | 允许的文件类型 |
| autoImport | boolean | false | 是否自动开始导入 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | (visible: boolean) | 更新visible状态 |
| success | (result: TExcelImportResultObject) | 导入成功事件 |
| error | (error: any) | 导入失败事件 |
| close | - | 关闭弹窗事件 |
| cancel | - | 取消导入事件 |

## 高级用法

### 自定义配置

```vue
<ExcelStepImport
  v-model:visible="showImport"
  template-key="custom_template"
  title="自定义导入"
  width="1400px"
  :max-file-size="20"
  :accept-types="['.xlsx', '.xls', '.csv']"
  :auto-import="true"
  @success="handleSuccess"
  @error="handleError"
/>
```

### 监听所有事件

```vue
<ExcelStepImport
  v-model:visible="showImport"
  template-key="template_key"
  @success="handleSuccess"
  @error="handleError"
  @close="handleClose"
  @cancel="handleCancel"
/>
```

## 组件结构

```
ExcelStepImport/
├── src/
│   ├── ExcelStepImport.vue      # 主组件
│   ├── ValidationStep.vue       # 数据校验步骤
│   ├── ExecuteStep.vue         # 导入执行步骤
│   ├── EditableCell.vue        # 可编辑单元格
│   └── types.ts                # 类型定义
├── index.ts                    # 组件导出
├── example.vue                 # 使用示例
└── README.md                   # 说明文档
```

## 依赖的API接口

组件依赖以下API接口（来自 `@/apis/excel-import`）：

- `getTemplateInfo(key)` - 获取模板信息
- `downloadTemplate(key)` - 下载模板文件
- `validateExcel(key, formData)` - 验证Excel数据
- `validateRow(key, rowData)` - 验证单行数据（新增）
- `importExcel(key, cacheId, dataList)` - 导入Excel数据
- `downloadFailures(key, cacheId)` - 下载失败数据

## 下拉选择功能

组件支持字段下拉选择功能，当字段配置了选项时，编辑时会显示下拉选择器而不是输入框。

### 字段选项配置

有两种方式配置字段选项：

#### 1. 在字段定义中直接配置

```typescript
{
  fieldName: 'department',
  displayName: '部门',
  hasOptions: true,
  options: [
    { value: 'tech', label: '技术部' },
    { value: 'market', label: '市场部' },
    { value: 'hr', label: '人事部' }
  ]
}
```

#### 2. 在模板的fieldOptions中配置

```typescript
{
  fields: [
    {
      fieldName: 'status',
      displayName: '状态',
      hasOptions: true
    }
  ],
  fieldOptions: {
    status: [
      { value: 'active', label: '在职' },
      { value: 'leave', label: '请假' },
      { value: 'resign', label: '离职' }
    ]
  }
}
```

### 下拉选择特性

- **🔍 可筛选**: 支持输入关键字筛选选项
- **🚫 不可输入**: 只能从预定义选项中选择，不允许自定义输入
- **📝 显示优化**: 表格中显示选项的label，而不是value
- **⚡ 快速选择**: 点击单元格即可打开下拉选择器

## 实时验证功能

组件支持实时验证功能，当用户编辑单元格数据后，会自动调用后端单行验证API，实时更新错误状态。

### 验证流程

1. **用户编辑**: 用户点击单元格进行编辑
2. **数据更新**: 编辑完成后更新本地数据
3. **调用验证**: 自动调用 `POST /common/excel/validate/{key}/row` API
4. **状态更新**: 根据验证结果更新单元格错误状态
5. **提交控制**: 检查所有行是否有错误，控制提交按钮状态

### 后端API

#### 单行验证接口

```http
POST /common/excel/validate/{key}/row
Content-Type: application/json

{
  "row": 2,
  "data": {
    "name": "张三",
    "age": 25,
    "department": "tech"
  },
  "messages": []
}
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "row": 2,
    "data": {
      "name": "张三",
      "age": 25,
      "department": "tech"
    },
    "messages": [
      {
        "type": "ERROR",
        "column": "age",
        "message": "年龄必须在18-65之间"
      }
    ],
    "type": "ERROR"
  }
}
```

### 按钮逻辑

#### 确认导入按钮
- **显示条件**: 在数据校验步骤
- **启用条件**: 所有数据都没有ERROR类型的错误
- **功能**: 导入所有验证通过的数据

#### 强制导入按钮
- **显示条件**: 有ERROR但存在有效数据时
- **启用条件**: 至少有一行数据没有ERROR
- **功能**: 弹出确认对话框，只导入有效数据

#### 上一步按钮
- **禁用条件**: 导入成功完成后
- **功能**: 防止用户在导入完成后返回修改

### 实现细节

- **ExcelImportHandler.validateRow()**: 新增的单行验证方法
- **ValidationStep.handleCellUpdate()**: 编辑后自动调用验证
- **ExcelStepImport.canImport**: 严格检查是否可以提交
- **ExcelStepImport.hasValidData**: 检查是否有有效数据可导入

## 数据类型

### TExcelImportResultObject

```typescript
interface TExcelImportResultObject {
  id?: string
  total?: number
  success?: number
  error?: number
  warning?: number
  dataList?: TExcelDataInfoObject[]
  message?: string
}
```

### TExcelTemplateInfo

```typescript
interface TExcelTemplateInfo {
  key?: string
  name?: string
  description?: string
  sheetName?: string
  titleRows?: number
  maxRows?: number
  fields?: TExcelFieldInfo[]
  fieldOptions?: any
}
```

## 样式定制

组件使用CSS变量，可以通过覆盖CSS变量来定制样式：

```css
.excel-step-import-dialog {
  --el-color-primary: #your-color;
  --el-color-success: #your-color;
  --el-color-warning: #your-color;
  --el-color-error: #your-color;
}
```

## 注意事项

1. 确保后端已实现对应的Excel导入API接口
2. 模板key必须在后端已注册
3. 文件大小限制可根据服务器配置调整
4. 组件依赖Element Plus，确保已正确安装和配置

## 更新日志

### v1.0.0
- 初始版本发布
- 支持三步导入流程
- 支持拖拽上传和在线编辑
- 完整的错误处理和进度显示
