@use 'sass:map';
@use 'mixins' as *;

// 后台主体窗口左右间距
$main-space: 16px;
$primary-light: #FFCD78;

// --ba-background
$bg-color: () !default;
$bg-color: map.merge(
  (
    '': #f5f5f5,
    'overlay': #ffffff,
  ),
  $bg-color
);

// --ba-border-color
// eaeef1
$border-color: () !default;
$border-color: map.merge(
  (
    '': #f6f6f6,
  ),
  $border-color
);

:root {
  @include set-css-var-value('main-space', $main-space);
  @include set-css-var-value('color-primary-light', $primary-light);
  @include set-component-css-var('bg-color', $bg-color);
  @include set-component-css-var('border-color', $border-color);

  // --el-color-white: #000;
  // --el-color-primary: #FFCD78;
  // --el-color-primary-light-3: #ffd285;
  // --el-color-primary-light-5: #ffdfa7;
  // --el-color-primary-light-7: #ffe4b5;
  // --el-color-primary-light-8: #ffeccd;
  // --el-color-primary-light-9: #fff9ef;
  // --el-button-hover-text-color: #000;
}
