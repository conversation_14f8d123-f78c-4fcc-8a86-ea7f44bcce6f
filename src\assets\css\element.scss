


/* 修复 Chrome 浏览器输入框内选中字符行高异常的bug-s */
.el-input .el-input__inner {
  line-height: calc(var(--el-input-height, 40px) - 4px);
}
/* 修复 Chrome 浏览器输入框内选中字符行高异常的bug-e */

.datetime-picker {
  height: 32px;
  padding-top: 0;
  padding-bottom: 0;
}
.el-divider__text.is-center {
  transform: translateX(-50%) translateY(-62%);
}

.el-menu {
  user-select: none;
}

.el-table {
  --el-table-border-color: var(--ba-border-color);
}

.el-card {
  border: none;
}
.el-card__header {
  border-bottom: 1px solid var(--el-border-color-extra-light);
}
.el-textarea__inner {
  padding: 5px 11px;
}

/* dialog滚动条-s */
.el-overlay-dialog,
.ba-scroll-style {
  scrollbar-width: none;
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }
  &::-webkit-scrollbar-thumb {
    background: #eaeaea;
    border-radius: var(--el-border-radius-base);
    box-shadow: none;
    -webkit-box-shadow: none;
  }
  &:hover {
    &::-webkit-scrollbar-thumb:hover {
      background: #c8c9cc;
    }
  }
}
.el-dialog__header,
.el-message-box__header {
  text-align: left;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--ba-bg-color);
}
.el-dialog__header {
  .el-dialog__title {
    font-size: 16px;
  }
}
.el-button:focus-visible {
  outline: none !important;
}
/* dialog滚动条-e */

.el-table .el-table__row .el-table__cell {
  padding: 12px 0px;
}

.el-table__body-wrapper .el-scrollbar__bar.is-horizontal {
  height: 10px;
}

.el-dialog__footer {
  padding: var(--el-dialog-padding-primary);
}
.el-dialog__header {
  padding: var(--el-dialog-padding-primary);
}
.el-dialog {
  padding: 0;
}

.el-dialog__headerbtn {
  top: 6px;
}

.el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.el-table__header {
  .cell {
    color: var(--el-text-color-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
