<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsDeephole01a } from '@/apis/data'
import type { TDataDrillingTotalWithPlanStats } from '@/apis/model'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDataDrillingTotalWithPlanStats[]>([])
const lastViewType = ref('monthly')

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate
    }

    const res = await apiDataStatsDeephole01a(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取潜孔钻进数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: {
        type: 'value'
      },
      series: []
    }
  }

  const xAxisData = chartData.value.map((item: TDataDrillingTotalWithPlanStats) => formatDisplayDate(item))
  const planData = chartData.value.map((item: TDataDrillingTotalWithPlanStats) => item.planProgressMeters || 0)
  const actualData = chartData.value.map((item: TDataDrillingTotalWithPlanStats) => item.totalProgressMeters || 0)

  const getMax = (arr: number[]) => {
    let max = Math.round(Math.max(...arr))
    if (max === 0) return 100
    return (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1)
  }
  const yMax = getMax([...planData, ...actualData])

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        let result = params[0].axisValue + '<br/>';
        params.forEach((item: any) => {
          const value = item.value;
          const seriesName = item.seriesName;
          const color = item.color;

          result += `<div style="display:flex;align-items:center;margin:5px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:${color};border-radius:50%;margin-right:5px;"></span>
            ${seriesName}: <strong>${value}米</strong>
          </div>`;
        });
        return result;
      }
    },
    legend: {
      data: ['计划进尺', '实际进尺'],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: ['5%', '5%'],
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          rotate: 0,
          margin: 15,
          overflow: 'truncate',
          formatter: (value: string) => {
            if (lastViewType.value === 'daily') {
              return value
            } else if (lastViewType.value === 'monthly') {
              return value.replace('年', '年\n')
            }
            return value
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '进尺(米)',
        min: 0,
        // max: yMax,
        // interval: yMax / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#5B9BD5',
          },
        },
        nameTextStyle: {
          color: '#5B9BD5',
        },
      },
    ],
    series: [
      {
        name: '计划进尺',
        type: 'bar',
        barWidth: '30%',
        data: planData,
        itemStyle: {
          color: '#5B9BD580',
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#333',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
      {
        name: '实际进尺',
        type: 'bar',
        barWidth: '30%',
        data: actualData,
        itemStyle: {
          color: '#5B9BD5',
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#333',
          fontSize: 11,
          fontWeight: 'normal',
        },
      },
    ],
  }
})
</script>
