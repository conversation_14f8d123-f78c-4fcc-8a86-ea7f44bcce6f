<script setup lang="ts">
import {
  apiSystemLayoutCreate,
  apiSystemLayoutId,
  apiSystemLayoutList,
  apiSystemLayoutUpdate,
  type TSysLayoutConfig,
} from '@/apis/system'
import { json5, json5ParseLinter } from 'codemirror-json5'
import JSON5 from 'json5'
import { linter } from '@codemirror/lint'
import { oneDark } from '@codemirror/theme-one-dark'
import { Delete, Document, Edit, Finished, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElPagination } from 'element-plus'
import { GridStack } from 'gridstack'
import 'gridstack/dist/gridstack.min.css'
import { nextTick, onMounted, ref, watch } from 'vue'
import { Codemirror } from 'vue-codemirror'

// 可用的Widget类型
const widgetTypes: WidgetType[] = [
  {
    component: 'CommonTable',
    title: '通用表格',
    defaultSize: { w: 12, h: 3 },
    props: {
      params: { date: true },
      code: 'xxxx', // 输入代码
      hideHeader: false, // 渲染时是否隐藏表头
    },
  },
]

// 响应式数据
const widgetPanelVisible = ref(false)
const editingWidget = ref<any>(null)
const editDialogVisible = ref(false)
const editDialogFormData = ref({
  title: '',
  component: '',
  props: '',
})

const saveLayoutDialogVisible = ref(false)
const saveLayoutFormData = ref({
  layoutName: '',
  layoutType: '',
})
const layoutName = ref('')
const layoutType = ref('')
const isFirstChange = ref(true)
// const currentLayoutId = ref<number | null>(null)
let grid: GridStack | null = null
const widgetList = ref<
  {
    id: string
    x: number
    y: number
    w: number
    h: number
    component: string
    title: string
    props: Object
  }[]
>([])

// 系统布局列表相关
const systemLayoutList = ref<TSysLayoutConfig[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const selectedLayoutId = ref<number | null>(null)

// 编辑模式相关
const isCodeMode = ref(false)
const layoutJsonCode = ref('')
const hasUnsavedChanges = ref(false)
const originalLayoutData = ref<string>('')

// CodeMirror 配置
const codeMirrorExtensions = [json5(), linter(json5ParseLinter()), oneDark]

// Widget类型定义
interface WidgetType {
  title: string
  component: string
  defaultSize: { w: number; h: number }
  props: any
}



// 监听变化以检测未保存状态
watch(
  [layoutJsonCode],
  () => {
    if (isFirstChange.value) {
      isFirstChange.value = false
      return
    }
    markAsChanged()
  },
  { deep: true }
)

// 标记为已修改
const markAsChanged = () => {
  hasUnsavedChanges.value = true
}

// 检查是否有未保存的更改
const checkUnsavedChanges = async (): Promise<boolean> => {
  if (!hasUnsavedChanges.value) return true

  try {
    await ElMessageBox.confirm(
      '当前布局有未保存的更改，是否保存？',
      '未保存的更改',
      {
        confirmButtonText: '保存',
        cancelButtonText: '不保存',
        distinguishCancelAndClose: true,
        type: 'warning',
      }
    )
    await saveCurrentLayout()
    return true
  } catch (action) {
    if (action === 'cancel') {
      return true // 用户选择不保存
    }
    return false // 用户取消操作
  }
}

// 初始化GridStack
const initGridStack = () => {
  grid = GridStack.init({
    cellHeight: 60,
    margin: 10,
    minRow: 1,
    animate: true,
    float: false,
    resizable: {
      handles: 'e, se, s, sw, w, nw, n, ne',
      autoHide: true,
    },
    acceptWidgets: true,
    removable: true,
    column: 12,
    maxRow: 0,
  })

  // 监听变化事件
  grid.on('change', () => {
    if (isFirstChange.value) {
      isFirstChange.value = false
      return
    }
    markAsChanged()
  })
  grid.on('added', () => {
    if (selectedLayoutId.value === -1 && widgetList.value.length === 1) {
      markAsChanged()
    }
  })
}

// 添加Widget
const addWidget = (widgetType: WidgetType) => {
  const widgetId = `widget_${Date.now()}`
  if (isCodeMode.value) {
    try {
      const data = JSON5.parse(layoutJsonCode.value)
      const newWidget = {
        id: widgetId,
        x: 0,
        y: 0,
        w: widgetType.defaultSize.w,
        h: widgetType.defaultSize.h,
        component: widgetType.component,
        title: widgetType.title,
        props: widgetType.props,
      }
      data.widgets.unshift(newWidget)
      layoutJsonCode.value = JSON5.stringify(data, null, 2)
    } catch (error) {
      ElMessage.error('布局数据解析失败')
      return
    }
    markAsChanged()
  } else {
    if (!grid) return
    const newWidget = {
      id: widgetId,
      x: 0,
      y: 0,
      w: widgetType.defaultSize.w,
      h: widgetType.defaultSize.h,
      component: widgetType.component,
      title: widgetType.title,
      props: widgetType.props,
    }
    widgetList.value.push(newWidget)
    nextTick(() => {
      grid!.makeWidget(widgetId)
    })
  }
  widgetPanelVisible.value = false
  ElMessage.success(`已添加组件：${widgetType.title}`)
}

// 右键菜单相关
const contextMenuVisible = ref(false)
const contextMenuPosition = ref({ x: 0, y: 0 })
const contextMenuWidgetId = ref('')

const onGridStackItemRightClick = (event: MouseEvent, widgetId: string) => {
  event.preventDefault()
  contextMenuPosition.value = { x: event.clientX, y: event.clientY }
  contextMenuWidgetId.value = widgetId
  contextMenuVisible.value = true
}

// 删除Widget
const removeWidget = (widgetId: string) => {
  ElMessageBox.confirm('确定要删除这个组件吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      const index = widgetList.value.findIndex((item) => item.id === widgetId)
      if (index > -1) {
        widgetList.value.splice(index, 1)
      }

      const element = document.getElementById(widgetId)
      if (element && grid) {
        grid.removeWidget(element)
      }

      contextMenuVisible.value = false
      ElMessage.success('组件已删除')
    })
    .catch(() => {
      contextMenuVisible.value = false
    })
}

// 编辑Widget名称
const editWidgetName = (widgetId: string) => {
  const widget = widgetList.value.find((item) => item.id === widgetId)
  if (widget) {
    editingWidget.value = widget
    editDialogFormData.value.title = widget.title
    editDialogFormData.value.component = widget.component
    editDialogFormData.value.props = JSON5.stringify(widget.props, null, 2)
    editDialogVisible.value = true
    contextMenuVisible.value = false
  }
}

// 确认修改名称
const confirmEdit = () => {
  if (!editDialogFormData.value.title.trim()) {
    ElMessage.warning('组件名称不能为空')
    return
  }

  if (!editDialogFormData.value.component.trim()) {
    ElMessage.warning('组件路径不能为空')
    return
  }

  let props = {}
  if (editDialogFormData.value.props) {
    try {
      props = JSON5.parse(editDialogFormData.value.props.trim())
    } catch (error) {
      ElMessage.warning('组件属性格式错误')
      return
    }
  }

  if (editingWidget.value) {
    editingWidget.value.title = editDialogFormData.value.title.trim()
    editingWidget.value.component = editDialogFormData.value.component.trim()
    editingWidget.value.props = props
    editDialogVisible.value = false
    editingWidget.value = null
    ElMessage.success('组件信息已更新')
  }
}

// 获取系统布局列表
const getSystemLayoutList = async () => {
  try {
    loading.value = true
    const query: TSysLayoutConfig = {}
    const result = await apiSystemLayoutList(query)
    systemLayoutList.value = result.rows || []
    total.value = result.total || 0
  } catch (error) {
    console.error('获取系统布局列表失败:', error)
    ElMessage.error('获取系统布局列表失败')
  } finally {
    loading.value = false
  }
}

// 选择布局
const selectLayout = async (layout: TSysLayoutConfig) => {
  if (!(await checkUnsavedChanges())) return

  selectedLayoutId.value = layout.id || null
  await loadSystemLayout(layout)
}

// 新建布局
const createNewLayout = async () => {
  if (!(await checkUnsavedChanges())) return
  selectedLayoutId.value = -1
  // currentLayoutId.value = null
  layoutName.value = ''
  layoutType.value = ''
  widgetList.value = []
  layoutJsonCode.value = ''
  isFirstChange.value = true
  hasUnsavedChanges.value = false

  if (grid) {
    grid.removeAll()
  }

  ElMessage.success('已创建新布局')
}

// 切换编辑模式
const toggleEditMode = () => {
  if (isCodeMode.value) {
    // 从代码模式切换到布局模式
    try {
      const config = JSON5.parse(layoutJsonCode.value)
      if (config.widgets) {
        widgetList.value = [...config.widgets]
        nextTick(() => {
          widgetList.value.forEach((item) => {
            grid!.makeWidget(item.id)
          })
        })
      }
      isCodeMode.value = false
    } catch (error) {
      ElMessage.error('JSON格式错误，无法切换到布局模式')
      return
    }
  } else {
    // 从布局模式切换到代码模式
    if (grid) {
      loadNewWidgetList()
      const saveData = {
        widgets: widgetList.value,
        timestamp: new Date().toISOString(),
      }
      layoutJsonCode.value = JSON5.stringify(saveData, null, 2)
      grid!.removeAll()
      widgetList.value = []
    }
    isCodeMode.value = true
  }
}

const loadNewWidgetList = () => {
  const gridData = grid!.save() as any[]
  const idNameMap = widgetList.value.reduce(
    (map, item) => {
      map[item.id] = {
        title: item.title,
        component: item.component,
        props: item.props,
      }
      return map
    },
    {} as Record<string, { title: string; component: string; props: Object }>
  )
  widgetList.value = gridData.map((item) => {
    return {
      id: item.id,
      x: item.x,
      y: item.y,
      w: item.w,
      h: item.h,
      component: idNameMap[item.id].component,
      props: idNameMap[item.id].props,
      title: idNameMap[item.id].title,
    }
  })
  return widgetList.value
}

// 保存当前布局
const saveCurrentLayout = async () => {
  // 每次保存都弹出表单，让用户输入layoutName和layoutType
  // 如果已有选中的布局，则预填充布局名称和类型
  if (selectedLayoutId.value && selectedLayoutId.value > 0) {
    const currentLayout = systemLayoutList.value.find(
      (item) => item.id === selectedLayoutId.value
    )
    if (currentLayout) {
      saveLayoutFormData.value.layoutName = currentLayout.layoutName || ''
      saveLayoutFormData.value.layoutType = currentLayout.layoutType || ''
    }
  }
  saveLayoutDialogVisible.value = true
}

const otherSaveCurrentLayout = () => {
  selectedLayoutId.value = -1
  saveCurrentLayout()
}

// 保存布局到系统
const saveLayoutToSystem = async () => {
  saveLayoutDialogVisible.value = false

  if (!saveLayoutFormData.value.layoutName.trim()) {
    ElMessage.warning('请输入布局名称')
    return
  }

  try {
    let saveData
    if (isCodeMode.value) {
      saveData = JSON5.parse(layoutJsonCode.value)
    } else {
      loadNewWidgetList()
      saveData = {
        widgets: widgetList.value,
        timestamp: new Date().toISOString(),
      }
    }

    const layoutConfig: TSysLayoutConfig = {
      layoutName: saveLayoutFormData.value.layoutName.trim(),
      layoutType: saveLayoutFormData.value.layoutType || 'gridstack',
      configJson: JSON5.stringify(saveData),
    }

    if (selectedLayoutId.value && selectedLayoutId.value > 0) {
      layoutConfig.id = selectedLayoutId.value
      await apiSystemLayoutUpdate(layoutConfig)
      ElMessage.success('布局更新成功')
    } else {
      const { id, ...data } = layoutConfig
      const resultId = await apiSystemLayoutCreate(data)
      selectedLayoutId.value = resultId
      ElMessage.success('布局保存成功')
    }

    hasUnsavedChanges.value = false
    isFirstChange.value = true
    originalLayoutData.value = JSON5.stringify(saveData)
    await getSystemLayoutList()
    layoutName.value = saveLayoutFormData.value.layoutName
    layoutType.value = saveLayoutFormData.value.layoutType
    // 清空表单数据
    saveLayoutFormData.value.layoutName = ''
    saveLayoutFormData.value.layoutType = ''
  } catch (error) {
    console.error('保存布局失败:', error)
    ElMessage.error('保存布局失败')
  }
}

// 加载系统布局
const loadSystemLayout = async (layout: TSysLayoutConfig) => {
  try {
    if (!layout.id) return

    const layoutData = await apiSystemLayoutId(layout.id)
    if (layoutData.configJson) {
      const config = JSON5.parse(layoutData.configJson)
      // 设置当前布局信息
      // currentLayoutId.value = layout.id
      layoutName.value = layout.layoutName || ''
      layoutType.value = layout.layoutType || ''
      hasUnsavedChanges.value = false
      isFirstChange.value = true
      originalLayoutData.value = layoutData.configJson

      if (isCodeMode.value) {
        layoutJsonCode.value = JSON5.stringify(config, null, 2)
      } else {
        isFirstChange.value = true
        // 清空当前布局
        widgetList.value = [...config.widgets]
        nextTick(() => {
          if (grid) {
            grid.removeAll()
            widgetList.value.forEach((item) => {
              grid!.makeWidget(item.id)
            })
          }
        })
      }
    }
  } catch (error) {
    console.error('加载布局失败:', error)
    ElMessage.error('加载布局失败')
  }
}

// 清空布局
const clearLayout = () => {
  ElMessageBox.confirm('确定要清空所有组件吗？', '确认清空', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      if (isCodeMode.value) {
        try {
          const data = JSON5.parse(layoutJsonCode.value)
          data.widgets = []
          layoutJsonCode.value = JSON5.stringify(data)
        } catch (error) {
          ElMessage.error({
            message: 'JSON5解析失败:' + error,
          })
        }
      } else {
        widgetList.value = []
        if (grid) {
          grid.removeAll()
        }
        // currentLayoutId.value = null
      }

      ElMessage.success('布局已清空')
    })
    .catch(() => {
      // 用户取消
    })
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initGridStack()
  })
  getSystemLayoutList()
})
</script>

<template>
  <div class="flex h-full p-6">
    <!-- 左侧布局列表 -->
    <div
      class="w-300px bg-white flex flex-col"
      style="border-right: 1px solid #efefef"
    >
      <!-- 左侧头部 -->
      <div class="p-4" style="border-bottom: 1px solid #efefef">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-medium">布局列表</h3>
          <el-button type="primary" @click="createNewLayout">
            <el-icon>
              <Plus />
            </el-icon>
            新建
          </el-button>
        </div>
      </div>

      <!-- 布局列表 -->
      <div class="flex-1 overflow-y-auto p-4">
        <div v-loading="loading" class="space-y-3">
          <div
            v-for="layout in systemLayoutList"
            :key="layout.id"
            class="p-3 border border-gray-200 rounded cursor-pointer hover:border-blue-400 transition-colors"
            :class="{
              'border-blue-500 bg-blue-50': selectedLayoutId === layout.id,
            }"
            @click="selectLayout(layout)"
          >
            <div class="text-xs">{{ layout.id }}</div>
            <div class="font-medium text-sm">{{ layout.layoutName }}</div>
            <div class="text-xs text-gray-500 mt-1">
              {{ layout.layoutType || 'chart' }}
            </div>
            <div class="text-xs text-gray-400 mt-1">
              {{ layout.updateTime }}
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="p-4 flex justify-end" style="border-top: 1px solid #efefef">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, prev, pager, next"
          small
          @size-change="getSystemLayoutList"
          @current-change="getSystemLayoutList"
        />
      </div>
    </div>

    <!-- 右侧编辑区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 右侧工具栏 -->
      <template v-if="selectedLayoutId">
        <div class="bg-white p-4" style="border-bottom: 1px solid #efefef">
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
              <h2 class="text-xl font-medium">
                {{ layoutName || '未命名布局' }}
                <span
                  v-if="hasUnsavedChanges"
                  class="text-orange-500 text-sm ml-2"
                  >*未保存</span
                >
              </h2>
            </div>

            <div class="flex space-x-2">
              <!-- 模式切换按钮 -->
              <el-button @click="toggleEditMode">
                <!-- <el-icon>
                  <Code v-if="!isCodeMode" />
                  <Grid v-else />
                </el-icon> -->
                {{ isCodeMode ? '布局模式' : '代码模式' }}
              </el-button>

              <!-- 添加组件按钮 -->
              <el-button type="primary" @click="widgetPanelVisible = true">
                <el-icon>
                  <Plus />
                </el-icon>
                添加组件
              </el-button>

              <!-- 保存按钮 -->
              <el-button type="success" @click="saveCurrentLayout">
                <el-icon>
                  <Finished />
                </el-icon>
                保存
              </el-button>

              <!-- 另存为按钮 -->
              <el-button @click="otherSaveCurrentLayout">
                <el-icon>
                  <Document />
                </el-icon>
                另存为
              </el-button>

              <!-- 清空按钮 -->
              <el-button @click="clearLayout">
                <el-icon>
                  <Delete />
                </el-icon>
                清空
              </el-button>
            </div>
          </div>
        </div>
      </template>
      <!-- 编辑内容区域 -->
      <div class="flex-1 overflow-hidden">
        <!-- 空状态 -->
        <div
          v-show="!selectedLayoutId"
          class="h-full flex items-center justify-center bg-gray-50"
        >
          <div class="text-center">
            <el-icon class="text-6xl text-gray-300 mb-4">
              <Document />
            </el-icon>
            <p class="text-gray-500 mb-4">请先新建或选择一个布局开始编辑</p>
            <el-button type="primary" @click="createNewLayout">
              <el-icon>
                <Plus />
              </el-icon>
              新建布局
            </el-button>
          </div>
        </div>

        <!-- 布局模式 -->
        <div
          v-show="selectedLayoutId && !isCodeMode"
          class="h-full overflow-auto"
        >
          <div class="bg-white rounded shadow min-h-full">
            <div class="grid-stack p-4">
              <div
                v-for="w in widgetList"
                :key="w.id"
                class="grid-stack-item"
                :gs-x="w.x"
                :gs-y="w.y"
                :gs-w="w.w"
                :gs-h="w.h"
                :gs-id="w.id"
                :id="w.id"
                @contextmenu="onGridStackItemRightClick($event, w.id)"
              >
                <div
                  class="grid-stack-item-content bg-blue-50 flex items-center justify-center border border-blue-200 rounded"
                >
                  {{ w.title }} ({{ w.component }})
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 代码模式 -->
        <div v-show="selectedLayoutId && isCodeMode" class="h-full">
          <codemirror
            v-model="layoutJsonCode"
            :style="{ height: '100%' }"
            :extensions="codeMirrorExtensions"
            @change="markAsChanged"
          />
        </div>
      </div>
    </div>

    <!-- Widget选择面板 -->
    <el-drawer
      v-model="widgetPanelVisible"
      title="选择组件"
      direction="rtl"
      size="400px"
    >
      <div class="grid grid-cols-2 gap-4">
        <div
          v-for="widget in widgetTypes"
          :key="widget.component"
          class="flex flex-col gap-2 items-center justify-center cursor-pointer rounded p-4 hover:border-blue-400 transition-colors"
          style="border: 1px solid #efefef"
          @click="addWidget(widget)"
        >
          <div class="widget-name font-medium">{{ widget.title }}</div>
          <div class="widget-size text-sm text-gray-500">
            {{ widget.defaultSize.w }}x{{ widget.defaultSize.h }}
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 保存布局对话框 -->
    <el-dialog
      v-model="saveLayoutDialogVisible"
      title="另存为布局"
      width="500px"
    >
      <div class="p-4">
        <el-form label-width="100px">
          <el-form-item label="布局名称" required>
            <el-input
              v-model="saveLayoutFormData.layoutName"
              placeholder="请输入布局名称"
            />
          </el-form-item>
          <el-form-item label="布局类型">
            <el-input
              v-model="saveLayoutFormData.layoutType"
              placeholder="请输入布局类型（可选）"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="saveLayoutDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveLayoutToSystem">保存</el-button>
      </template>
    </el-dialog>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="fixed bg-white border border-gray-200 rounded shadow-lg py-2 z-50"
      :style="{
        left: contextMenuPosition.x + 'px',
        top: contextMenuPosition.y + 'px',
      }"
      @click.stop
    >
      <div
        class="px-4 py-2 hover:bg-gray-100 cursor-pointer"
        @click="editWidgetName(contextMenuWidgetId)"
      >
        <el-icon class="mr-2">
          <Edit />
        </el-icon>
        编辑
      </div>
      <div
        class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-red-600"
        @click="removeWidget(contextMenuWidgetId)"
      >
        <el-icon class="mr-2">
          <Delete />
        </el-icon>
        删除组件
      </div>
    </div>

    <!-- 点击其他地方关闭右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="fixed inset-0 z-40"
      @click="contextMenuVisible = false"
    ></div>

    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑" width="800px">
      <div class="p-4">
        <el-form label-width="100px">
          <el-form-item label="标题">
            <el-input
              v-model="editDialogFormData.title"
              placeholder="请输入标题"
            />
          </el-form-item>
          <el-form-item label="组件路径">
            <el-input
              v-model="editDialogFormData.component"
              placeholder="请输入组件路径"
            />
          </el-form-item>
          <el-form-item label="额外参数">
            <el-input
              type="textarea"
              :rows="10"
              v-model="editDialogFormData.props"
              placeholder="请输入额外参数"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmEdit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>
