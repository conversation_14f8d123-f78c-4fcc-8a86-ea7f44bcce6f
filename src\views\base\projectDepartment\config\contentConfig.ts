export const tableItem: BaseTableItem[] = [
  {
    prop: 'projectDepartmentId',
    label: '项目部ID',
    width: '80',
  },

  {
    prop: 'projectDepartmentName',
    label: '项目部名称'
  },
  {
    prop: 'status',
    label: '状态',
    width: '100',
    slotName: 'status',
    isDict: true
  },

  {
    prop: 'startTime',
    label: '启用时间'
  },
  {
    prop: 'endTime',
    label: '终止时间'
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },

]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'projectDepartmentId',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
