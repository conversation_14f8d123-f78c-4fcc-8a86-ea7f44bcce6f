<template>
  <div class="p-6 overflow-auto">
    <div class="p-6 bg-white flex flex-col gap-4  min-w-[1400px]">
      <div class="flex gap-4 items-center">
        <ChartDate1 v-model="chartDate" :allowViewTypes="['daily', 'weekly', 'monthly']" />
        <el-button type="warning" @click="handleExport" :loading="exportLoading">导出</el-button>
      </div>

      <MergeTable ref="mergeTableRef" :id="id" :viewType="chartDate.viewType" :date="chartDate.date" />
    </div>
  </div>
</template>
<script setup lang="ts">
import ChartDate1 from '@/components/ChartDate/ChartDate1.vue'
import MergeTable from '@/components/MergeTable/MergeTable.vue'
import { exportTablesToExcel } from '@/utils/excel-export'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'

const chartDate = ref({
  viewType: 'daily' as 'daily' | 'weekly' | 'monthly' | 'yearly',
  date: '',
})

const exportLoading = ref(false)
const mergeTableRef = ref<InstanceType<typeof MergeTable>>()

const id = computed(() => {
  const map = {
    daily: 3,
    weekly: 5,
    monthly: 4,
    yearly: 6,
  }
  return map[chartDate.value.viewType as keyof typeof map] || 3
})

// 格式化日期用于文件名
const formatDateForFilename = computed(() => {
  if (chartDate.value.viewType === 'daily')
    return dayjs(chartDate.value.date).format('YYYY年MM月DD日')
  if (chartDate.value.viewType === 'weekly')
    return `${dayjs(chartDate.value.date).startOf('week').format('YYYY年MM月DD日')}-${dayjs(chartDate.value.date).endOf('week').format('MM月DD日')}`
  if (chartDate.value.viewType === 'monthly') {
    return dayjs(chartDate.value.date).format('YYYY年MM月')
  }
  if (chartDate.value.viewType === 'yearly') return dayjs(chartDate.value.date).format('YYYY年')
  return ''
})

const handleExport = async () => {
  try {
    exportLoading.value = true

    // 等待一下确保DOM已更新
    await nextTick()

    // 获取MergeTable组件的DOM元素
    const mergeTableEl = mergeTableRef.value?.$el
    if (!mergeTableEl) {
      ElMessage.error('未找到表格数据')
      return
    }

    // 检查是否有表格正在加载
    const loadingElements = mergeTableEl.querySelectorAll('.el-loading-mask')
    if (loadingElements.length > 0) {
      ElMessage.warning('表格数据正在加载中，请稍后再试')
      return
    }

    // 检查是否有表格数据
    const tables = mergeTableEl.querySelectorAll('table')
    if (!tables || tables.length === 0) {
      ElMessage.error('未找到表格数据')
      return
    }

    // 检查表格是否有数据
    let hasData = false
    tables.forEach((table: HTMLTableElement) => {
      const tbody = table.querySelector('tbody')
      if (tbody && tbody.querySelectorAll('tr').length > 0) {
        hasData = true
      }
    })

    if (!hasData) {
      ElMessage.warning('表格中没有数据可导出')
      return
    }

    // 获取报表标题
    const titleElement = mergeTableEl.querySelector('.text-center.text-xl.font-bold')
    const reportTitle = titleElement?.textContent?.trim() || `报表数据_${formatDateForFilename.value}`

    // 使用导出工具函数
    await exportTablesToExcel(mergeTableEl, {
      title: reportTitle,
      filename: `${reportTitle}_${new Date().getTime()}`,
      columnWidth: 15
    })

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}
</script>
