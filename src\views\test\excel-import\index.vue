<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>Excel导入测试</span>
        </div>
      </template>

      <!-- 基础使用示例 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="12">
          <el-card class="inner-card">
            <template #header>
              <div class="card-header">
                <span>基础使用</span>
              </div>
            </template>
            <p>最简单的使用方式，只需要指定模板key</p>
            <el-button type="primary" @click="openBasicImport">
              基础导入
            </el-button>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="inner-card">
            <template #header>
              <div class="card-header">
                <span>自定义配置</span>
              </div>
            </template>
            <p>自定义弹窗大小、文件限制等配置</p>
            <el-button type="success" @click="openCustomImport">
              自定义导入
            </el-button>
          </el-card>
        </el-col>
      </el-row>

      <!-- 模板选择示例 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="24">
          <el-card class="inner-card">
            <template #header>
              <div class="card-header">
                <span>模板选择</span>
              </div>
            </template>
            <div class="template-selector">
              <el-select v-model="selectedTemplate" placeholder="选择导入模板" style="width: 200px; margin-right: 10px;">
                <el-option
                  v-for="template in templateOptions"
                  :key="template.key"
                  :label="template.name"
                  :value="template.key"
                />
              </el-select>
              <el-button
                type="warning"
                @click="openTemplateImport"
                :disabled="!selectedTemplate"
              >
                使用选中模板导入
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 基础导入组件 -->
      <ExcelStepImport
        v-model:visible="basicImportVisible"
        title="基础Excel导入"
        template-key="simple_test"
        @success="handleImportSuccess"
        @close="handleImportClose"
        @error="handleImportError"
      />

      <!-- 自定义配置导入组件 -->
      <ExcelStepImport
        v-model:visible="customImportVisible"
        title="自定义Excel导入"
        template-key="simple_test"
        width="1200px"
        :max-file-size="20"
        :accept-types="['.xlsx', '.xls', '.csv']"
        @success="handleImportSuccess"
        @close="handleImportClose"
        @error="handleImportError"
      />

      <!-- 模板选择导入组件 -->
      <ExcelStepImport
        v-model:visible="templateImportVisible"
        :title="`${selectedTemplateName}数据导入`"
        :template-key="selectedTemplate"
        @success="handleImportSuccess"
        @close="handleImportClose"
        @error="handleImportError"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { TExcelImportResultObject } from '@/apis/model.d'
import ExcelStepImport from '@/components/ExcelStepImport'
import { ElMessage } from 'element-plus'
import { ref, computed } from 'vue'

// 响应式数据
const basicImportVisible = ref(false)
const customImportVisible = ref(false)
const templateImportVisible = ref(false)
const selectedTemplate = ref('')

// 模板选项
const templateOptions = ref([
  { key: 'simple_test', name: '简单测试导入' },
  { key: 'user_import', name: '用户数据导入' },
  { key: 'product_import', name: '产品数据导入' },
  { key: 'order_import', name: '订单数据导入' }
])

// 计算属性
const selectedTemplateName = computed(() => {
  const template = templateOptions.value.find(t => t.key === selectedTemplate.value)
  return template?.name || '数据'
})

// 方法
const openBasicImport = () => {
  basicImportVisible.value = true
}

const openCustomImport = () => {
  customImportVisible.value = true
}

const openTemplateImport = () => {
  if (!selectedTemplate.value) {
    ElMessage.warning('请先选择一个模板')
    return
  }
  templateImportVisible.value = true
}

const handleImportSuccess = (result: TExcelImportResultObject) => {
  ElMessage.success(`导入成功: ${result.success} 条数据`)
  console.log('导入结果:', result)
}

const handleImportError = (error: any) => {
  ElMessage.error('导入失败，请检查数据格式')
  console.error('导入错误:', error)
}

const handleImportClose = () => {
  // 关闭所有导入弹窗
  basicImportVisible.value = false
  customImportVisible.value = false
  templateImportVisible.value = false
  console.log('导入弹窗已关闭')
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.mb20 {
  margin-bottom: 20px;
}

.mt20 {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.inner-card {
  margin-bottom: 20px;
}

.inner-card:last-child {
  margin-bottom: 0;
}

.template-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.inner-card p {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.5;
}

.inner-card .el-button {
  width: 100%;
}
</style>
