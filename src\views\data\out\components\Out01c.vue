<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsOut01c } from '@/apis/data'
import type { TDataMuckingOutDepartmentStats } from '@/apis/model'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDataMuckingOutDepartmentStats[]>([])
const lastViewType = ref('daily')

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate
    }

    const res = await apiDataStatsOut01c(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取采场出矿按项目部数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: [],
      },
      yAxis: {
        type: 'value',
        name: '出矿量(吨)',
      },
      series: [],
    }
  }

  // 按项目部分组数据
  const departmentMap = new Map()
  chartData.value.forEach((item: TDataMuckingOutDepartmentStats) => {
    const key = `${formatDisplayDate(item)}`
    if (!departmentMap.has(key)) {
      departmentMap.set(key, {})
    }
    const dateData = departmentMap.get(key)
    dateData[item.projectDepartmentName || `部门${item.projectDepartmentId}`] = item.totalTons || 0
  })

  // 获取所有项目部名称
  const allDepartments = new Set<string>()
  chartData.value.forEach((item: TDataMuckingOutDepartmentStats) => {
    allDepartments.add(item.projectDepartmentName || `部门${item.projectDepartmentId}`)
  })
  const departmentNames = Array.from(allDepartments).sort()

  // 生成X轴数据
  const xAxisData = Array.from(departmentMap.keys()).sort()

  // 为每个部门生成系列数据
  const series = departmentNames.map((deptName, index) => {
    const data = xAxisData.map(date => {
      const dateData = departmentMap.get(date)
      return dateData[deptName] || 0
    })

    // 颜色数组
    const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#36CFC9', '#722ED1', '#13C2C2']
    
    return {
      name: deptName,
      type: 'bar',
      data: data,
      itemStyle: {
        color: colors[index % colors.length],
      },
    }
  })

  // 计算Y轴最大值
  const allValues = series.flatMap(s => s.data)
  const maxValue = Math.max(...allValues)
  const yMax = maxValue > 0 ? Math.ceil(maxValue * 1.1) : 100

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params: any) {
        let result = params[0].axisValue + '<br/>'
        params.forEach((item: any) => {
          const value = item.value
          const seriesName = item.seriesName
          const color = item.color

          result += `<div style="display:flex;align-items:center;margin:5px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:${color};border-radius:50%;margin-right:5px;"></span>
            ${seriesName}: <strong>${value}吨</strong>
          </div>`
        })
        return result
      },
    },
    legend: {
      data: departmentNames,
      bottom: 0,
      type: 'scroll',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: ['5%', '5%'],
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          formatter: function (value: string) {
            return value
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '出矿量(吨)',
        min: 0,
        max: yMax,
        interval: yMax / 5,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#409EFF',
          },
        },
        axisLabel: {
          formatter: '{value}',
        },
        nameTextStyle: {
          color: '#409EFF',
        },
      },
    ],
    series: series,
  }
})
</script>
