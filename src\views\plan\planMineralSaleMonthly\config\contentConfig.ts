export const tableItem: BaseTableItem[] = [
  {
    prop: 'planDate',
    label: '计划月份'
  },
  {
    prop: 'ironConcentrateVolume',
    label: '铁精粉量'
  },
  {
    prop: 'concentratorBinsStockVolume',
    label: '选矿厂矿仓存矿销售'
  },
  {
    prop: 'serviceShaftSurfaceStockVolume',
    label: '入措施井地表存矿销售'
  },
  {
    prop: 'rawOreGrade',
    label: '原矿品位-TFe'
  },
  {
    prop: 'stockVolume',
    label: '库存'
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },

]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
