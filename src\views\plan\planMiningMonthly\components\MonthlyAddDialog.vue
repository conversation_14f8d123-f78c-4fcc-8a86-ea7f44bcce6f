<script setup lang="ts">
import { planBaseUrl } from '@/api/config/base.js'
import { request } from '@/utils/hsj/service/index'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'
import { apiPlanPlanMiningMonthlyBatch } from '@/apis/plan'
import type { TPlanMiningMonthlyBatchDto } from '@/apis/model.d'

const pageName = 'planMiningMonthly'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  projectOptions: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const selectedMonth = ref('')

const tableData = ref<any[]>([])
const loading = ref(false)

const formRef = ref<any>(null)
const rules = {
  selectedMonth: [{ required: true, message: '请选择月份', trigger: 'change' }],
}

const fetchPlans = async () => {
  if (!selectedMonth.value) {
    ElMessage.warning('请先选择月份')
    return
  }

  loading.value = true
  try {
    const res = await request<any>({
      url: `${planBaseUrl}/${pageName}/list`,
      method: 'get',
      params: {
        planDate: selectedMonth.value,
        pageSize: 999,
      },
    })

    if (res.code === 200) {
      tableData.value =
        res.rows.map((item: any) => {
          return {
            ...item,
            projectDepartmentId: String(item.projectDepartmentId),
          }
        }) || []
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取计划数据失败:', error)
    ElMessage.error('获取计划数据失败')
  } finally {
    loading.value = false
  }
}

const addRow = () => {
  if (!selectedMonth.value) {
    ElMessage.warning('请先选择月份')
    return
  }

  tableData.value.push({
    planDate: selectedMonth.value,
    projectDepartmentId: '',
    driftMeter: '',
    rawOreVolume: '',
    supportMeter: '',
    fillingVolume: '',
    dthMeter: '',
    deepHoleMeter: '',
    oreOutputVolume: '',
    isNew: true,
  })

  setTimeout(() => {
    const selectElements = document.querySelectorAll('.monthly-add-dialog .el-table .el-select')
    if (selectElements && selectElements.length > 0) {
      const targetSelect = selectElements[selectElements.length - 1]
      if (targetSelect) {
        const input = targetSelect.querySelector('input')
        if (input) {
          (input as HTMLElement).focus()
        } else {
          (targetSelect as HTMLElement).click()
        }
      }
    }
  }, 100)
}

const deleteRow = (index: number, _row: any) => {
  tableData.value.splice(index, 1)
}

const handleDialogClosed = () => {
  selectedMonth.value = ''
  tableData.value = []
}

const saveAll = async () => {
  if (!selectedMonth.value) {
    ElMessage.warning('请先选择月份')
    return
  }

  if (tableData.value.length === 0) {
    ElMessage.warning('没有数据需要保存')
    return
  }

  for (const item of tableData.value) {
    if (!item.projectDepartmentId) {
      ElMessage.warning('请选择项目')
      return
    }
  }

  console.log('提交数据:', JSON.stringify(tableData.value, null, 2))

  loading.value = true
  try {
    // 准备提交数据
    const batchData: TPlanMiningMonthlyBatchDto[] = tableData.value.map((item) => ({
      id: item.id,
      planMonth: selectedMonth.value,
      projectDepartmentId: Number(item.projectDepartmentId),
      driftMeter: item.driftMeter,
      rawOreVolume: item.rawOreVolume,
      supportMeter: item.supportMeter,
      fillingVolume: item.fillingVolume,
      dthMeter: item.dthMeter,
      deepHoleMeter: item.deepHoleMeter,
      oreOutputVolume: item.oreOutputVolume,
      operationType: item.isNew ? 'add' : 'edit',
      remark: item.remark || ''
    }))

    // 使用批量保存API
    await apiPlanPlanMiningMonthlyBatch(batchData)

    ElMessage.success('保存成功')
    dialogVisible.value = false
    emit('refresh')
  } catch (error) {
    console.error('保存数据失败:', error)
    ElMessage.error('保存数据失败')
  } finally {
    loading.value = false
  }
}

watch(selectedMonth, (newVal) => {
  if (newVal) {
    tableData.value = []
    fetchPlans()
  }
})

const getAvailableProjectOptions = (currentRow: any) => {
  const selectedProjectIds = tableData.value
    .filter((row) => row !== currentRow && row.projectDepartmentId)
    .map((row) => row.projectDepartmentId)

  return props.projectOptions.filter(
    (item: any) => !selectedProjectIds.includes((item as any).value)
  )
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="按月添加采矿计划"
    destroy-on-close
    width="1400px"
    :close-on-click-modal="false"
    append-to-body
    class="monthly-add-dialog"
    @closed="handleDialogClosed"
  >
    <el-form
      ref="formRef"
      :model="{ selectedMonth }"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="选择月份" prop="selectedMonth">
        <el-date-picker
          v-model="selectedMonth"
          type="month"
          placeholder="请选择月份"
          format="YYYY-MM"
          value-format="YYYYMM"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="success" @click="addRow">
          <el-icon><component :is="Plus" /></el-icon> 添加行
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      max-height="600px"
    >


      <el-table-column label="项目" prop="projectDepartmentId">
        <template #default="{ row }">
          <el-select
            v-model="row.projectDepartmentId"
            placeholder="请选择项目"
            style="width: 100%"
          >
            <el-option
              v-for="item in getAvailableProjectOptions(row)"
              :key="(item as any).value"
              :label="(item as any).label"
              :value="(item as any).value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column label="掘进米数" prop="driftMeter">
        <template #default="{ row }">
          <el-input v-model="row.driftMeter" placeholder="请输入掘进米数" />
        </template>
      </el-table-column>

      <el-table-column label="原矿量" prop="rawOreVolume">
        <template #default="{ row }">
          <el-input v-model="row.rawOreVolume" placeholder="请输入原矿量" />
        </template>
      </el-table-column>

      <el-table-column label="支护米数" prop="supportMeter">
        <template #default="{ row }">
          <el-input v-model="row.supportMeter" placeholder="请输入支护米数" />
        </template>
      </el-table-column>

      <el-table-column label="充填量" prop="fillingVolume">
        <template #default="{ row }">
          <el-input v-model="row.fillingVolume" placeholder="请输入充填量" />
        </template>
      </el-table-column>

      <el-table-column label="潜孔米数" prop="dthMeter">
        <template #default="{ row }">
          <el-input v-model="row.dthMeter" placeholder="请输入潜孔米数" />
        </template>
      </el-table-column>

      <el-table-column label="中深孔米数" prop="deepHoleMeter">
        <template #default="{ row }">
          <el-input v-model="row.deepHoleMeter" placeholder="请输入中深孔米数" />
        </template>
      </el-table-column>

      <el-table-column label="出矿量" prop="oreOutputVolume">
        <template #default="{ row }">
          <el-input v-model="row.oreOutputVolume" placeholder="请输入出矿量" />
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120">
        <template #default="{ row, $index }">
          <el-button type="danger" link @click="deleteRow($index, row)">
            <el-icon><component :is="Delete" /></el-icon> 删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveAll" :loading="loading"
        >保 存</el-button
      >
    </template>
  </el-dialog>
</template>

<style>
.monthly-add-dialog .el-dialog__body {
  padding: 20px !important;
}

.monthly-add-dialog .el-table {
  margin-top: 15px;
  width: 100% !important;
}

.monthly-add-dialog .el-dialog__header,
.monthly-add-dialog .el-dialog__footer {
  padding: 15px 20px !important;
}

.monthly-add-dialog .el-dialog__body .el-table__body-wrapper {
  overflow-y: auto;
}

.monthly-add-dialog .el-form-item {
  margin-bottom: 18px;
}
</style>
