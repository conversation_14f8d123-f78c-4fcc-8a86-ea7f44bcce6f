<template>
  <div class="default-main page p-6 bg-white rounded shadow">
    <div class="mb-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="font-bold text-2xl text-gray-800">潜孔钻进</h2>
        <div class="flex space-x-2">
          <el-button class="!px-4" icon="DataAnalysis">数据分析</el-button>
          <el-button class="!px-4" type="primary" icon="Setting" @click="goDataManagement">数据管理</el-button>
        </div>
      </div>

      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex flex-wrap items-center gap-2">
          <el-select 
            v-model="chartType" 
            placeholder="选择图表类型" 
            style="width: 150px"
            class="mr-4"
          >
            <el-option label="整体" value="01a" />
            <el-option label="按项目部" value="01b" />
          </el-select>
          <ChartDateRange1 v-model="chartDateRange" />
        </div>
      </div>
    </div>

    <div class="w-full">
      <Dth01a
        v-if="chartType === '01a'" 
        :date-range="chartDateRange" 
      />
      <Dth01b
        v-if="chartType === '01b'" 
        :date-range="chartDateRange" 
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChartDateRange1 from '@/components/ChartDateRange/ChartDateRange1.vue'
import Dth01a from './components/Dth01a.vue'
import Dth01b from './components/Dth01b.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const goDataManagement = () => {
  router.push('/dth/data')
}

// 图表类型选择
const chartType = ref('01a')

// 日期范围
const chartDateRange = ref({
  viewType: 'daily', // daily 或 monthly
  startDate: '',
  endDate: '',
})
</script>
