<template>
  <div v-loading="loading" :style="'height:' + height">
    <iframe
      :src="url"
      frameborder="no"
      style="width: 100%; height: 100%"
      scrolling="auto"
    />
  </div>
</template>

<script setup lang="ts">
type Props = {
  src: string
}
const { src } = defineProps<Props>()

const height = ref(document.documentElement.clientHeight - 94.5 + 'px;')
const loading = ref(true)
const url = computed(() => src)

onMounted(() => {
  setTimeout(() => {
    loading.value = false
  }, 300)
  window.onresize = function temp() {
    height.value = document.documentElement.clientHeight - 94.5 + 'px;'
  }
})
</script>
