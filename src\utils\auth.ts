import Cookies from 'js-cookie'
import useStorage from '@/utils/hsj/useStorage'
const TokenKey = 'Admin-Token'

export function getToken() {
  Cookies.get(TokenKey)
  return useStorage.get(TokenKey)
}

export function setToken(token: string) {
  Cookies.set(Token<PERSON><PERSON>, token, { expires: 7 })
  return useStorage.set(Token<PERSON>ey, token)
}

export function removeToken() {
  Cookies.remove(TokenKey)
  return useStorage.remove(Token<PERSON>ey)
}
