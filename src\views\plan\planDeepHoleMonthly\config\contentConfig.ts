export const tableItem: BaseTableItem[] = [
  {
    prop: 'planDate',
    label: '计划月份'
  },
  {
    prop: 'projectDepartmentName',
    label: '项目'
  },
  {
    prop: 'workingFaceName',
    label: '工作面'
  },
  {
    prop: 'stopeName',
    label: '采场'
  },
  {
    prop: 'deepHoleMeter',
    label: '中深孔钻机米数'
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },

]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
