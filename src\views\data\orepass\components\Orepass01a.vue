<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsOrepass01a } from '@/apis/data'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<any[]>([])
const lastViewType = ref('daily')

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate
    }

    const res = await apiDataStatsOrepass01a(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取溜井放矿数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: {
        type: 'value'
      },
      series: []
    }
  }

  const xAxisData = chartData.value.map((item: any) => formatDisplayDate(item))
  const seriesData1 = chartData.value.map((item: any) => item.totalOreTons || 0)

  const getMax = (arr: number[]) => {
    let max = Math.round(Math.max(...arr))
    return (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1)
  }
  const y1Max = getMax(seriesData1)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    legend: {
      data: ['溜矿量'],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: ['5%', '5%'], // 左右各留出5%的间距
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          // 旋转标签
          rotate: 0, //
          margin: 15, // 标签与坐标轴的间距
          // 超出显示省略号
          overflow: 'truncate',
          // 标签格式化
          formatter: (value: string) => {
            if (lastViewType.value === 'daily') {
              return value
            } else if (lastViewType.value === 'monthly') {
              return value.replace('年', '年\n')
            }
            return value
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '溜矿量(吨)',
        min: 0,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#409EFF',
          },
        },
        nameTextStyle: {
          color: '#409EFF',
        },
      },
    
    ],
    series: [
      {
        name: '溜矿量',
        type: 'bar',
        barWidth: '60%',
        yAxisIndex: 0,
        data: seriesData1,
        itemStyle: {
          color: '#409EFF',
        },
      },
     
    ],
  }
})

</script>
