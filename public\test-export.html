<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel导出测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .export-btn {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .export-btn:hover {
            background-color: #337ecc;
        }
        .export-btn:disabled {
            background-color: #c0c4cc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <button class="export-btn" onclick="testExport()">导出Excel</button>
        
        <div class="title">矿井日报 (2025年06月12日)</div>
        
        <table>
            <thead>
                <tr>
                    <th rowspan="2">序号</th>
                    <th rowspan="2">名称</th>
                    <th rowspan="2">单位</th>
                    <th rowspan="2">月计划</th>
                    <th rowspan="2">日产量</th>
                    <th rowspan="2">月累计</th>
                    <th rowspan="2">完成率</th>
                    <th rowspan="2">月累计超欠</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>原矿</td>
                    <td>t</td>
                    <td>0</td>
                    <td>66567.98</td>
                    <td>193506.47</td>
                    <td>0.00%</td>
                    <td>193506.47</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>采场出矿</td>
                    <td>t</td>
                    <td>131000</td>
                    <td>23853</td>
                    <td>194921</td>
                    <td>44.91%</td>
                    <td>-239079</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>0#-26-9</td>
                    <td>t</td>
                    <td>40000</td>
                    <td>2006</td>
                    <td>16456</td>
                    <td>41.14%</td>
                    <td>-23544</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>0#-26-5</td>
                    <td>t</td>
                    <td>40000</td>
                    <td>2524</td>
                    <td>17836</td>
                    <td>44.59%</td>
                    <td>-22164</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>0#-16-1</td>
                    <td>t</td>
                    <td>0</td>
                    <td>3656</td>
                    <td>16952</td>
                    <td>0.00%</td>
                    <td>16952</td>
                </tr>
            </tbody>
        </table>
        
        <table>
            <thead>
                <tr>
                    <th>设备名称</th>
                    <th>运行时间</th>
                    <th>故障时间</th>
                    <th>空闲时间</th>
                    <th>设备利用率</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>挖掘机A</td>
                    <td>18.5</td>
                    <td>2.0</td>
                    <td>3.5</td>
                    <td>77.1%</td>
                </tr>
                <tr>
                    <td>挖掘机B</td>
                    <td>20.0</td>
                    <td>1.5</td>
                    <td>2.5</td>
                    <td>83.3%</td>
                </tr>
                <tr>
                    <td>装载机A</td>
                    <td>19.2</td>
                    <td>1.8</td>
                    <td>3.0</td>
                    <td>80.0%</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
    <script>
        // 简化版的导出函数用于测试
        function testExport() {
            const container = document.querySelector('.test-container');
            const tables = container.querySelectorAll('table');
            const title = container.querySelector('.title').textContent.trim();
            
            // 创建工作簿
            const workbook = XLSX.utils.book_new();
            const mergedData = [];
            
            // 添加标题
            mergedData.push([title]);
            mergedData.push([]); // 空行
            
            // 处理每个表格
            tables.forEach(table => {
                // 处理表头
                const thead = table.querySelector('thead');
                if (thead) {
                    const headerRows = thead.querySelectorAll('tr');
                    headerRows.forEach(row => {
                        const rowData = [];
                        const cells = row.querySelectorAll('th');
                        cells.forEach(cell => {
                            const text = cell.textContent.trim();
                            const colspan = parseInt(cell.getAttribute('colspan') || '1');
                            rowData.push(text);
                            for (let i = 1; i < colspan; i++) {
                                rowData.push('');
                            }
                        });
                        if (rowData.length > 0) {
                            mergedData.push(rowData);
                        }
                    });
                }
                
                // 处理表体
                const tbody = table.querySelector('tbody');
                if (tbody) {
                    const bodyRows = tbody.querySelectorAll('tr');
                    bodyRows.forEach(row => {
                        const rowData = [];
                        const cells = row.querySelectorAll('td');
                        cells.forEach(cell => {
                            const text = cell.textContent.trim();
                            const colspan = parseInt(cell.getAttribute('colspan') || '1');
                            rowData.push(text);
                            for (let i = 1; i < colspan; i++) {
                                rowData.push('');
                            }
                        });
                        if (rowData.length > 0) {
                            mergedData.push(rowData);
                        }
                    });
                }
            });
            
            // 创建工作表
            const worksheet = XLSX.utils.aoa_to_sheet(mergedData);

            // 设置列宽
            const maxCols = Math.max(...mergedData.map(row => row.length));
            if (maxCols > 0) {
                const colWidths = Array(maxCols).fill(0).map(() => ({ wch: 15 }));
                worksheet['!cols'] = colWidths;
            }

            // 应用样式
            applyStyles(worksheet, mergedData, title, maxCols);

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(workbook, worksheet, '报表数据');
            
            // 生成Excel文件并下载
            const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
            const blob = new Blob([excelBuffer], { 
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
            });
            
            saveAs(blob, `${title}_${new Date().getTime()}.xlsx`);
        }

        // 应用Excel样式
        function applyStyles(worksheet, data, title, maxCols) {
            if (!worksheet['!ref']) return;

            const range = XLSX.utils.decode_range(worksheet['!ref']);
            let currentRow = 0;

            // 标题样式
            if (title && data.length > 0) {
                const titleCellRef = XLSX.utils.encode_cell({ r: currentRow, c: 0 });

                // 合并标题行
                if (maxCols && maxCols > 1) {
                    const mergeRange = {
                        s: { r: currentRow, c: 0 },
                        e: { r: currentRow, c: maxCols - 1 }
                    };
                    if (!worksheet['!merges']) worksheet['!merges'] = [];
                    worksheet['!merges'].push(mergeRange);
                }

                // 标题样式
                if (worksheet[titleCellRef]) {
                    worksheet[titleCellRef].s = {
                        font: { bold: true, sz: 16, color: { rgb: '000000' } },
                        alignment: { horizontal: 'center', vertical: 'center' },
                        fill: { fgColor: { rgb: 'FFFFFF' } },
                        border: {
                            top: { style: 'thin', color: { rgb: '000000' } },
                            bottom: { style: 'thin', color: { rgb: '000000' } },
                            left: { style: 'thin', color: { rgb: '000000' } },
                            right: { style: 'thin', color: { rgb: '000000' } }
                        }
                    };
                }
                currentRow += 2; // 标题行 + 空行
            }

            // 应用所有单元格的边框和基本样式
            for (let r = 0; r <= range.e.r; r++) {
                for (let c = 0; c <= range.e.c; c++) {
                    const cellRef = XLSX.utils.encode_cell({ r, c });
                    if (worksheet[cellRef]) {
                        // 基本样式：边框和居中对齐
                        const baseStyle = {
                            alignment: { horizontal: 'center', vertical: 'center' },
                            border: {
                                top: { style: 'thin', color: { rgb: '000000' } },
                                bottom: { style: 'thin', color: { rgb: '000000' } },
                                left: { style: 'thin', color: { rgb: '000000' } },
                                right: { style: 'thin', color: { rgb: '000000' } }
                            }
                        };

                        // 如果已有样式，合并样式
                        if (worksheet[cellRef].s) {
                            worksheet[cellRef].s = { ...worksheet[cellRef].s, ...baseStyle };
                        } else {
                            worksheet[cellRef].s = baseStyle;
                        }
                    }
                }
            }

            // 识别并应用表头样式
            for (let r = currentRow; r <= range.e.r; r++) {
                let textCellCount = 0;
                let totalCellCount = 0;

                // 检查这一行的内容特征
                for (let c = 0; c <= range.e.c; c++) {
                    const cellRef = XLSX.utils.encode_cell({ r, c });
                    const cell = worksheet[cellRef];
                    if (cell && cell.v) {
                        const cellValue = String(cell.v).trim();
                        if (cellValue) {
                            totalCellCount++;
                            // 如果不是纯数字，认为是文本
                            if (!/^\d+(\.\d+)?%?$/.test(cellValue) && !/^-?\d+(\.\d+)?$/.test(cellValue)) {
                                textCellCount++;
                            }
                        }
                    }
                }

                // 如果这一行超过60%的单元格是文本内容，认为是表头行
                if (totalCellCount > 0 && textCellCount / totalCellCount > 0.6) {
                    for (let c = 0; c <= range.e.c; c++) {
                        const cellRef = XLSX.utils.encode_cell({ r, c });
                        if (worksheet[cellRef]) {
                            // 合并现有样式和表头样式
                            const existingStyle = worksheet[cellRef].s || {};
                            worksheet[cellRef].s = {
                                ...existingStyle,
                                font: { bold: true, color: { rgb: '000000' } },
                                fill: { fgColor: { rgb: 'E6E6FA' } }, // 淡紫色背景
                                alignment: { horizontal: 'center', vertical: 'center' },
                                border: {
                                    top: { style: 'thin', color: { rgb: '000000' } },
                                    bottom: { style: 'thin', color: { rgb: '000000' } },
                                    left: { style: 'thin', color: { rgb: '000000' } },
                                    right: { style: 'thin', color: { rgb: '000000' } }
                                }
                            };
                        }
                    }
                }
            }
        }
    </script>
</body>
</html>
