<template>
  <div class="default-main page p-6 bg-white rounded shadow">
    <div class="mb-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="font-bold text-2xl text-gray-800">采场出矿</h2>
        <div class="flex space-x-2">
          <el-button class="!px-4" icon="DataAnalysis">数据分析</el-button>
          <el-button class="!px-4" type="primary" icon="Setting" @click="goDataManagement">数据管理</el-button>
        </div>
      </div>

      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex flex-wrap items-center gap-2">
          <el-select 
            v-model="chartType" 
            placeholder="选择图表类型" 
            style="width: 150px"
            class="mr-4"
          >
            <el-option label="总体" value="01a" />
            <el-option label="按采场" value="01b" />
            <el-option label="按项目部" value="01c" />
            <el-option label="按作业时段" value="01e" />
          </el-select>
          <ChartDateRange1 v-model="chartDateRange" v-show="chartType !== '01b'"/>
          <ChartDate1 v-model="chartDate" v-show="chartType === '01b'" />
        </div>
      </div>
    </div>

    <div class="w-full">
      <Out01a 
        v-if="chartType === '01a'" 
        :date-range="chartDateRange" 
      />
      <Out01b 
        v-if="chartType === '01b'" 
        :date="chartDate" 
      />
      <Out01c 
        v-if="chartType === '01c'" 
        :date-range="chartDateRange" 
      />
      <Out01e 
        v-if="chartType === '01e'" 
        :date-range="chartDateRange" 
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChartDateRange1 from '@/components/ChartDateRange/ChartDateRange1.vue'
import ChartDate1 from '@/components/ChartDate/ChartDate1.vue'
import Out01a from './components/Out01a.vue'
import Out01b from './components/Out01b.vue'
import Out01c from './components/Out01c.vue'
import Out01e from './components/Out01e.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const goDataManagement = () => {
  router.push('/demo/out_data')
}

// 图表类型选择
const chartType = ref('01a')

// 日期范围
const chartDateRange = ref({
  autoLoad: true,
  viewType: 'daily',
  startDate: '',
  endDate: '',
})

const chartDate = ref({
  viewType: 'daily',
  date: '',
})
</script>
