<template>
  <div class="flex items-center">
    <el-radio-group v-model="viewType" @change="handleDateTypeChange">
      <el-radio-button v-if="showViewType('daily')" label="daily"
        >每日</el-radio-button
      >
      <el-radio-button v-if="showViewType('weekly')" label="weekly"
        >每周</el-radio-button
      >
      <el-radio-button v-if="showViewType('monthly')" label="monthly"
        >每月</el-radio-button
      >
      <el-radio-button v-if="showViewType('yearly')" label="yearly"
        >每年</el-radio-button
      >
    </el-radio-group>

    <el-date-picker
      v-model="startDate"
      :type="getPickerType()"
      :format="getDateFormat()"
      :value-format="getValueFormat()"
      :placeholder="getStartPlaceholder()"
      :disabled-date="disableStartDate"
      class="ml-3"
    />
    <span class="mx-2">至</span>
    <el-date-picker
      v-model="endDate"
      :type="getPickerType()"
      :format="getDateFormat()"
      :value-format="getValueFormat()"
      :placeholder="getEndPlaceholder()"
      :disabled-date="disableEndDate"
    />

    <el-button type="primary" size="default" class="!ml-2" @click="triggerChange">
      查询
    </el-button>
    <el-button size="default" @click="handleReset">重置</el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import dayjs from 'dayjs'

type DateType = 'daily' | 'weekly' | 'monthly' | 'yearly'


const props = defineProps({
  defaultDateRange: {
    type: Object,
    default: () => ({
      viewType: 'daily',
      startDate: '',
      endDate: '',
    }),
  },
  modelValue: {
    type: Object,
    default: () => ({
      viewType: 'daily',
      startDate: '',
      endDate: '',
    }),
  },
  allowViewTypes: {
    type: Array as () => Array<DateType>,
    default: () => ['daily', 'weekly', 'monthly', 'yearly'],
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const viewType = ref<DateType>('daily')

// 检查视图类型是否显示
const showViewType = (type: DateType) => {
  return props.allowViewTypes.includes(type)
}
const startDate = ref<string>('')
const endDate = ref<string>('')

// 设置默认日期范围
const setViewTypeDefaultDateRange = () => {
  const today = dayjs()
   if (viewType.value === 'daily') {
    // // 如果是每月1号，则从上个月1号开始
    // let startDate =
    //   today.date() > 28 ? today.date(28) : today.subtract(1, 'month').date(28)
    // startDate = startDate.add(1, 'day')

    startDate.value = '2025-05-29' // TODO
    endDate.value = '2025-06-28' // TODO
  } else if (viewType.value === 'weekly') {
    // 当前周
    startDate.value = today
      .startOf('week')
      .subtract(11, 'week')
      .format('YYYY-MM-DD')
    endDate.value = today.endOf('week').format('YYYY-MM-DD')
  } else if (viewType.value === 'yearly') {
    // 过去5年到当前年
    startDate.value = today.subtract(4, 'year').format('YYYY')
    endDate.value = today.format('YYYY')
  } else {
    // 当年1月到当前月
    startDate.value = today.month(0).date(1).format('YYYY-MM')
    endDate.value = today.format('YYYY-MM')
  }

  triggerChange()
}

// 处理日期类型切换
const handleDateTypeChange = () => {
  setViewTypeDefaultDateRange()
}

// 获取选择器类型
const getPickerType = () => {
  if (viewType.value === 'daily') return 'date'
  if (viewType.value === 'weekly') return 'week'
  if (viewType.value === 'yearly') return 'year'
  return 'month'
}

// 获取日期格式
const getDateFormat = () => {
  if (viewType.value === 'daily') return 'YYYY年MM月DD日'
  if (viewType.value === 'weekly') return 'YYYY年ww周'
  if (viewType.value === 'yearly') return 'YYYY年'
  return 'YYYY年MM月'
}

// 获取值格式
const getValueFormat = () => {
  if (viewType.value === 'daily') return 'YYYY-MM-DD'
  if (viewType.value === 'weekly') return 'YYYY-MM-DD'
  if (viewType.value === 'yearly') return 'YYYY'
  return 'YYYY-MM'
}

// 获取开始日期占位符
const getStartPlaceholder = () => {
  if (viewType.value === 'daily') return '开始日期'
  if (viewType.value === 'weekly') return '开始周'
  if (viewType.value === 'yearly') return '开始年份'
  return '开始月份'
}

// 获取结束日期占位符
const getEndPlaceholder = () => {
  if (viewType.value === 'daily') return '结束日期'
  if (viewType.value === 'weekly') return '结束周'
  if (viewType.value === 'yearly') return '结束年份'
  return '结束月份'
}

// 限制开始日期
const disableStartDate = (time: Date) => {
  if (!endDate.value) return false
  return dayjs(time).isAfter(dayjs(endDate.value))
}

// 限制结束日期
const disableEndDate = (time: Date) => {
  if (!startDate.value) return false
  return dayjs(time).isBefore(dayjs(startDate.value))
}


const handleReset = () => {
  if(props.defaultDateRange && props.defaultDateRange.startDate && props.defaultDateRange.endDate && props.defaultDateRange.viewType) {
    startDate.value = props.defaultDateRange.startDate
    endDate.value = props.defaultDateRange.endDate
    viewType.value = props.defaultDateRange.viewType
  } else {
    setViewTypeDefaultDateRange()
  }
  triggerChange()
}

// 触发change事件
const triggerChange = () => {
  const start = startDate.value
  const end = endDate.value

  if (!start || !end) return

  const result = {
    viewType: viewType.value,
    startDate:
      viewType.value === 'monthly'
        ? dayjs(start).startOf('month').format('YYYY-MM-DD')
        : viewType.value === 'weekly'
          ? dayjs(start).startOf('week').format('YYYY-MM-DD')
          : viewType.value === 'yearly'
            ? dayjs(start).startOf('year').format('YYYY-MM-DD')
            : start,
    endDate:
      viewType.value === 'monthly'
        ? dayjs(end).startOf('month').format('YYYY-MM-DD')
        : viewType.value === 'weekly'
          ? dayjs(end).startOf('week').format('YYYY-MM-DD')
          : viewType.value === 'yearly'
            ? dayjs(end).endOf('year').format('YYYY-MM-DD')
            : end,
  }

  emit('update:modelValue', result)
  emit('change', result)
}

// 监听传入的 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (
      newVal &&
      newVal.viewType &&
      newVal.startDate &&
      newVal.endDate &&
      (newVal.viewType !== viewType.value ||
        newVal.startDate !== startDate.value ||
        newVal.endDate !== endDate.value)
    ) {
      // 确保视图类型在允许的范围内
      if (props.allowViewTypes.includes(newVal.viewType)) {
        viewType.value = newVal.viewType
      } else if (props.allowViewTypes.length > 0) {
        // 如果不在允许范围内，则使用第一个允许的类型
        viewType.value = props.allowViewTypes[0]
      }
      startDate.value = newVal.startDate
      endDate.value = newVal.endDate
      triggerChange()
    } else if (!startDate.value || !endDate.value) {
      // 如果没有传入值且当前没有值，则设置默认值
      setViewTypeDefaultDateRange()
    }
  },
  { immediate: true }
)
</script>
