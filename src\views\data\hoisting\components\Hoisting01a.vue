<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsHoisting01a } from '@/apis/data'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<any[]>([])
const lastViewType = ref('daily')

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate
    }

    const res = await apiDataStatsHoisting01a(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取提升量数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: {
        type: 'value'
      },
      series: []
    }
  }

  const xAxisData = chartData.value.map((item: any) => formatDisplayDate(item))
  const seriesData1 = chartData.value.map((item: any) => item.totalBuckets || 0)
  const seriesData2 = chartData.value.map((item: any) => item.totalWeight || 0)

  const getMax = (arr: number[]) => {
    let max = Math.round(Math.max(...arr))
    return (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1)
  }
  const y1Max = getMax(seriesData2)
  const y2Max = getMax(seriesData1)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      formatter: function(params: any) {
        let result = params[0].axisValue + '<br/>';
        params.forEach((item: any) => {
          const value = item.value;
          const seriesName = item.seriesName;
          const color = item.color;
          let unit = '';

          if (seriesName === '提升量') {
            unit = '吨';
          } else if (seriesName === '斗数') {
            unit = '斗';
          }

          result += `<div style="display:flex;align-items:center;margin:5px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:${color};border-radius:50%;margin-right:5px;"></span>
            ${seriesName}: <strong>${value}${unit}</strong>
          </div>`;
        });
        return result;
      }
    },
    legend: {
      data: ['提升量', '斗数'],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: ['5%', '5%'],
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          rotate: 0,
          margin: 15,
          overflow: 'truncate',
          formatter: (value: string) => {
            if (lastViewType.value === 'daily') {
              return value
            } else if (lastViewType.value === 'monthly') {
              return value.replace('年', '年\n')
            }
            return value
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '提升量(吨)',
        min: 0,
        max: y1Max,
        interval: y1Max / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#409EFF',
          },
        },
        nameTextStyle: {
          color: '#409EFF',
        },
      },
      {
        type: 'value',
        name: '斗数(斗)',
        min: 0,
        max: y2Max,
        interval: y2Max / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#06EDC3',
          },
        },
        nameTextStyle: {
          color: '#06EDC3',
        },
      },
    ],
    series: [
      {
        name: '提升量',
        type: 'bar',
        barWidth: '60%',
        yAxisIndex: 0,
        data: seriesData2,
        itemStyle: {
          color: '#409EFF',
        },
      },
      {
        name: '斗数',
        type: 'line',
        yAxisIndex: 1,
        data: seriesData1,
        itemStyle: {
          color: '#06EDC3',
        },
        lineStyle: {
          width: 2,
        },
      },
    ],
  }
})
</script>
