import { apiBaseFaceListAll, apiBaseOrePassListAll, apiBasePeriodListAll, apiBaseProjectDepartmentListAll, apiBaseStopeListAll } from '@/apis/base'
import { TBaseOrePass, TBaseProjectDepartment, TBaseStope, TBaseWorkingFace, TBaseWorkingPeriod } from '@/apis/model'
import { toString } from '@/utils/toString'

export type DictOptions = {
  label: string
  value: string
}

export const getPeriodList = async (): Promise<DictOptions[]> => {
  const res = await apiBasePeriodListAll()
  if (res) {
    return res.map((item: TBaseWorkingPeriod) => ({
      label: item.workingPeriodName!,
      value: toString(item.workingPeriodId!),
    }))
  }
  return []
}

export const getFaceList = async (): Promise<DictOptions[]> => {
  const res = await apiBaseFaceListAll()
  if (res) {
    return res.map((item: TBaseWorkingFace) => ({
      label: item.workingFaceName!,
      value: toString(item.workingFaceId!),
    }))
  }
  return []
}

export const getStopeList = async (workingFaceId?: string | number): Promise<DictOptions[]> => {
  // 如果提供了工作面ID，则按工作面ID过滤采场
  const query = workingFaceId ? { workingFaceId: Number(workingFaceId) } : {}
  const res = await apiBaseStopeListAll(query)
  if (res) {
    return res.map((item: TBaseStope) => ({
      label: item.stopeName!,
      value: toString(item.stopeId!),
      parentValue: item.workingFaceId
    }))
  }
  return []
}

export const getOrePassList = async (): Promise<DictOptions[]> => {
  const res = await apiBaseOrePassListAll()
  if (res) {
    return res.map((item: TBaseOrePass) => ({
      label: item.orePassName!,
      value: toString(item.orePassId!),
    }))
  }
  return []
}

export const getProjectDepartmentList = async (): Promise<DictOptions[]> => {
  const res = await apiBaseProjectDepartmentListAll()
  if (res) {
    return res.map((item: TBaseProjectDepartment) => ({
      label: item.projectDepartmentName!,
      value: toString(item.projectDepartmentId!),
    }))
  }
  return []
}

