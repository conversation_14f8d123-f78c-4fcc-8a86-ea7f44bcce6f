import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'
import fetch from 'node-fetch'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const MATCH_PATHS = ['*']

const API_JSON_URL = 'http://localhost:8080/v3/api-docs/default' // 数据结构参考 api.json
const API_GEN_DIR = path.resolve(__dirname, '../src/apis')
const MODEL_FILE = path.join(API_GEN_DIR, 'model.d.ts')

let allSchemas = {}
const jumpSchemas = new Set(['AjaxResult', 'TableDataInfo'])

// 确保目录存在
async function ensureDir(dir) {
  try {
    await fs.access(dir)
  } catch {
    await fs.mkdir(dir, { recursive: true })
  }
}

// 将路径转换为有效的 TypeScript 类型名
function toTypeName(name) {
  return (
    'T' +
    name
      .split(/[^a-zA-Z0-9]/)
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
      .join('')
  )
}

// 将路径转换为有效的函数名
function toFunctionName(path) {
  const name = path
    .replace(/^\//, '')
    .split(/[^a-zA-Z0-9]/)
    .filter(Boolean)
    .map((part, index) =>
      index === 0 ? part : part.charAt(0).toUpperCase() + part.slice(1)
    )
    .join('')

  return 'api' + name[0].toUpperCase() + name.slice(1)
}

// 生成参数类型
function generateParamType(params) {
  const properties = []

  for (const param of params) {
    const schema = param.schema || {}
    let type = 'any'

    if (schema.type === 'integer') {
      type = 'number'
    } else if (schema.type === 'boolean') {
      type = 'boolean'
    } else if (schema.type === 'string') {
      type = 'string'
    } else if (schema.$ref) {
      type = toTypeName(schema.$ref.split('/').pop())
    } else if (schema.type === 'array') {
      if (schema.items?.$ref) {
        const itemType = toTypeName(schema.items.$ref.split('/').pop())
        type = `${itemType}[]`
      } else {
        type = 'any[]'
      }
    }

    // Special handling for batch APIs - if param name is 'data' and schema is an array
    // we should directly use the array type without wrapping it in an object
    if (param.name === 'data' && schema.type === 'array' && schema.items?.$ref) {
      return toTypeName(schema.items.$ref.split('/').pop()) + '[]'
    }

    properties.push(`${param.name}${param.required ? '' : '?'}: ${type}`)
  }

  return `{
  ${properties.join(';\n  ')}${properties.length ? ';' : ''}
}`
}

// 生成响应类型
function generateResponseType(responses, path = '', operationId = '') {
  const successResponse = responses?.['200'] || responses?.['201']
  if (!successResponse) return null

  // 检查是否为文件下载或非JSON响应
  const contentTypes = Object.keys(successResponse.content || {})
  if (
    contentTypes.includes('application/octet-stream') ||
    contentTypes.includes('application/vnd.ms-excel') ||
    contentTypes.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') ||
    contentTypes.includes('application/pdf') ||
    contentTypes.includes('application/zip')
  ) {
    // 文件下载类型，返回Blob类型
    return 'Blob'
  }

  // 检查路径或操作ID是否包含export或download关键字
  const pathLower = path.toLowerCase()
  const operationIdLower = operationId.toLowerCase()
  const isExportOrDownload = 
    pathLower.includes('export') || 
    pathLower.includes('download') || 
    operationIdLower.includes('export') || 
    operationIdLower.includes('download')

  const content =
    successResponse.content?.['*/*'] ||
    successResponse.content?.['application/json']
  
  // 如果是导出或下载接口，并且没有明确的内容类型，返回Blob
  if (!content && isExportOrDownload) {
    return 'Blob'
  }
  
  if (!content) return 'any'

  let schema = content.schema
  // 如果是导出或下载接口，并且没有明确的schema，返回Blob
  if (!schema && isExportOrDownload) {
    return 'Blob'
  }
  
  if (!schema) return 'any'

  const schemaName = schema.$ref?.split('/').pop()
  jumpSchemas.add(schemaName)
  if (schema.$ref && !schema.properties) {
    schema = allSchemas[schemaName]
  }

  // 检查是否包含 code, msg, data 字段
  if (
    schema.properties?.code !== undefined &&
    schema.properties?.msg !== undefined &&
    schema.properties?.data !== undefined
  ) {
    const dataSchema = schema.properties.data

    if (dataSchema.$ref) {
      // 直接返回引用类型
      return toTypeName(dataSchema.$ref.split('/').pop())
    } else if (dataSchema.type === 'array' && dataSchema.items?.$ref) {
      // 直接返回数组类型
      const itemType = toTypeName(dataSchema.items.$ref.split('/').pop())
      return `${itemType}[]`
    } else if (dataSchema.type === 'integer') {
      return 'number'
    } else if (dataSchema.type === 'boolean') {
      return 'boolean'
    } else if (dataSchema.type === 'string') {
      return 'string'
    } else if (dataSchema.type === 'object') {
      return 'any'
    }
  }

  // 不符合 code/msg/data 结构，返回 null 表示跳过此接口
  return null
}

// 生成请求函数
function generateApiFunction(path, method, operation, responseType = 'any') {
  const funcName = toFunctionName(path)
  const params = operation.parameters || []
  const queryParams = params.filter((p) => p.in === 'query')
  const pathParams = params.filter((p) => p.in === 'path')
  const bodyParam =
    params.find((p) => p.in === 'body') ||
    (operation.requestBody?.content?.['application/json']?.schema
      ? {
          name: 'data',
          required: true,
          schema: operation.requestBody.content['application/json'].schema,
        }
      : null)

  const args = []
  if (pathParams.length > 0) {
    args.push(`path: ${generateParamType(pathParams)}`)
  }

  if (queryParams.length > 0) {
    // If there's only one query parameter with the same name as the API group, use it directly
    if (queryParams.length === 1 && queryParams[0].schema?.$ref) {
      const paramName = queryParams[0].name
      const typeName = toTypeName(queryParams[0].schema.$ref.split('/').pop())
      // Check if the parameter name matches the type name (ignoring case and 'base' prefix)
      const normalizedParamName = paramName.toLowerCase()
      const normalizedTypeName = typeName.toLowerCase().replace(/^tbase/, '')
      
      if (normalizedParamName.includes(normalizedTypeName) || 
          normalizedTypeName.includes(normalizedParamName)) {
        args.push(`query: ${typeName}`)
      } else {
        args.push(`query: ${generateParamType(queryParams)}`)
      }
    } else {
      args.push(`query: ${generateParamType(queryParams)}`)
    }
  }

  if (bodyParam) {
    args.push(`data: ${generateParamType([bodyParam])}`)
  }

  const requestConfig = [`url: '${path}'`, `method: '${method}'`]

  if (queryParams.length > 0) {
    requestConfig.push('params: query')
  }

  if (bodyParam) {
    requestConfig.push('data')
  }

  // 修改返回逻辑，对于Blob类型直接返回响应，其他类型解构出 data 字段
  if (responseType === 'Blob') {
    return `export async function ${funcName}(${args.join(', ')}): Promise<AxiosResponse<Blob>> {
  return blob({\n    ${requestConfig.join(',\n    ')}\n  }); \n}\n\n`
  } else {
    return `export async function ${funcName}(${args.join(', ')}): Promise<${responseType}> {
  return request<{ data: ${responseType} }>({\n    ${requestConfig.join(',\n    ')}\n  }).then(res => res.data); \n}\n\n`
  }
}

// 生成类型定义
function generateTypeDef(name, schema) {
  if (!schema || !schema.properties) return ''

  if (jumpSchemas.has(name)) return ''

  const typeName = toTypeName(name)
  const properties = []

  if (
    schema.properties.code &&
    schema.properties.msg &&
    schema.properties.data
  ) {
    return null
  }

  for (const [propName, prop] of Object.entries(schema.properties)) {
    let type = 'any'

    if (prop.type === 'integer') {
      type = 'number'
    } else if (prop.type === 'boolean') {
      type = 'boolean'
    } else if (prop.type === 'string') {
      type = 'string'
      if (prop.format === 'date-time') {
        type = 'string' // 保持为 string 类型，因为前端通常处理的是 ISO 字符串
      }
    } else if (prop.$ref) {
      type = toTypeName(prop.$ref.split('/').pop())
    } else if (prop.type === 'array') {
      const itemsType = prop.items?.$ref
        ? toTypeName(prop.items.$ref.split('/').pop())
        : 'any'
      type = `${itemsType}[]`
    } else if (prop.type === 'object') {
      // 内联对象类型
      type = 'any' // 简化处理，实际可以根据需要递归生成
    }

    const required = schema.required?.includes(propName) ? '' : '?'
    properties.push(`  ${propName}${required}: ${type};`)
  }

  return `export interface ${typeName} {\n${properties.join('\n')}\n}`
}

// 主函数
async function main() {
  // 初始化全局类型定义
  global.typeDefs = []
  try {
    // 确保输出目录存在
    await ensureDir(API_GEN_DIR)

    // 获取 API 定义
    const response = await fetch(API_JSON_URL)
    const apiDoc = await response.json()

    allSchemas = apiDoc.components?.schemas

    // 按路径前缀分组
    const apiGroups = new Map()

    for (const [path, methods] of Object.entries(apiDoc.paths)) {
      // 检查路径是否匹配任何模式
      const matched = MATCH_PATHS.some((pattern) => {
        const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$')
        return regex.test(path)
      })

      if (!matched) continue

      // 提取路径的第一部分作为组名
      const groupName = path.split('/').filter(Boolean)[0] || 'common'
      if (!apiGroups.has(groupName)) {
        apiGroups.set(groupName, [])
      }

      // 添加路径和方法
      for (const [method, operation] of Object.entries(methods)) {
        if (typeof operation === 'object' && operation.operationId) {
          apiGroups.get(groupName).push({
            path,
            method,
            operation,
          })
        }
      }
    }

    // 生成类型定义
    const typeDefs = []
    for (const [name, schema] of Object.entries(
      apiDoc.components?.schemas || {}
    )) {
      const typeDef = generateTypeDef(name, schema)
      if (typeDef) {
        typeDefs.push(typeDef)
      }
    }

    // 添加内联类型定义
    if (global.typeDefs && global.typeDefs.length > 0) {
      typeDefs.push(...global.typeDefs)
    }

    // 写入类型定义文件
    await fs.writeFile(
      MODEL_FILE,
      `// 自动生成的类型定义，请勿手动修改\n\n${typeDefs.join('\n\n')}\n`,
      'utf-8'
    )

    // 为每个 API 组生成文件
    for (const [groupName, apis] of apiGroups) {
      if (apis.length === 0) continue

      const outputFile = path.join(API_GEN_DIR, `${groupName}.ts`)

      // 收集所有需要导入的类型
      const typeImports = new Set()
      const addToTypeImports = (t) => {
        if (
          [
            'any',
            'any[]',
            'number',
            'number[]',
            'string',
            'string[]',
            'boolean',
            'boolean[]',
            'Blob'
          ].includes(t)
        ) {
          return
        }
        if (t.endsWith('[]')) {
          t = t.slice(0, -2)
        }
        typeImports.add(t)
      }
      const apiFunctions = []

      // 先处理所有 API 函数，收集需要的类型
      for (const api of apis) {
        const responseType = generateResponseType(
          api.operation.responses, 
          api.path, 
          api.operation.operationId
        )
        if (!responseType) {
          // 跳过不符合规范的接口
          continue
        }
        addToTypeImports(responseType)

        // 处理参数中的类型
        const params = api.operation.parameters || []
        for (const param of params) {
          if (param.schema?.$ref) {
            const typeName = toTypeName(param.schema.$ref.split('/').pop())
            addToTypeImports(typeName)
          }
        }

        // 处理请求体中的类型
        const requestBody =
          api.operation.requestBody?.content?.['application/json']?.schema
        if (requestBody?.$ref) {
          const typeName = toTypeName(requestBody.$ref.split('/').pop())
          addToTypeImports(typeName)
        } else if (requestBody?.type === 'array' && requestBody.items?.$ref) {
          // Handle array request bodies (batch APIs)
          const typeName = toTypeName(requestBody.items.$ref.split('/').pop())
          addToTypeImports(typeName)
        }

        apiFunctions.push({
          func: generateApiFunction(
            api.path,
            api.method,
            api.operation,
            responseType
          ),
          responseType,
        })
      }

      // 构建导入语句
      const imports = ["import { request, blob } from '@/utils/hsj/service/index'"
        , "import { AxiosResponse } from 'axios'"
      ]

      if (typeImports.size > 0) {
        imports.push(
          `import { ${Array.from(typeImports).join(', ')} } from './model'`
        )
      }

      const fileContent = [
        '// 自动生成的 API 文件，请勿手动修改',
        '',
        ...imports,
        '',
        ...apiFunctions.map((api) => api.func),
        '',
      ].join('\n')

      await fs.writeFile(outputFile, fileContent, 'utf-8')
      console.log(`已生成文件: ${outputFile}`)
    }

    console.log('API 生成完成！')
  } catch (error) {
    console.error('生成 API 时出错:', error)
    process.exit(1)
  }
}

// 执行主函数
main()
