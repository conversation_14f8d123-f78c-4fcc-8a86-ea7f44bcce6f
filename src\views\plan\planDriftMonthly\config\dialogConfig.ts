export default (): BaseFormProps => {
  return {
    itemStyle: {
      padding: '0px 0px 0px 0px',
    },
    rules: {},
    formItems: [
      {
        field: 'planDate',
        config: {
          clearable: false,
          type: 'month',
          valueFormat: 'YYYYMM',
          format: 'YYYY-MM',
        },
        type: 'datepicker',
        label: '计划月份',
      },
      {
        field: 'projectDepartmentId',
        type: 'select',
        options: [],
        label: '项目部',
      },
      {
        field: 'workingFaceId',
        type: 'select',
        options: [],
        label: '工作面',
      },
      {
        field: 'stopeId',
        type: 'select',
        options: [],
        label: '采场',
      },
      {
        field: 'driftMeter',
        type: 'input',
        label: '掘进米数',
      },
      {
        field: 'driftVolume',
        type: 'input',
        label: '掘进方量',
      },
      {
        field: 'isPriority',
        type: 'select',
        options: [],
        label: '是否重点',
      },
    ],
    colLayout: {
      xl: 24,
      lg: 24,
      md: 24,
      sm: 24,
      xs: 24,
    },
    elFormConfig: {
      labelWidth: '100px',
    },
  }
}
