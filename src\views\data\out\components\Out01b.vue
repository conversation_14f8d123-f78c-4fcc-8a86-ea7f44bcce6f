<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsOut01b } from '@/apis/data'
import type { TDataMuckingOutStopeStats } from '@/apis/model'

interface Props {
  date: {
    viewType: string
    date: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDataMuckingOutStopeStats[]>([])
const lastViewType = ref('daily')

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, date } = props.date
    if (!date) {
      return
    }

    const params = {
      viewType,
      startDate: date,
      endDate: date,
    }

    const res = await apiDataStatsOut01b(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取采场出矿按采场数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.date, () => {
  if (props.date?.date) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'value',
        name: '出矿量(吨)',
      },
      yAxis: {
        type: 'category',
        data: [],
      },
      series: [],
    }
  }

  // 按采场汇总数据
  const stopeMap = new Map()
  chartData.value.forEach((item: TDataMuckingOutStopeStats) => {
    const stopeName = item.stopeName || `采场${item.stopeId}`
    if (stopeMap.has(stopeName)) {
      stopeMap.set(stopeName, stopeMap.get(stopeName) + (item.totalTons || 0))
    } else {
      stopeMap.set(stopeName, item.totalTons || 0)
    }
  })

  // 转换为数组并按出矿量排序
  const sortedData = Array.from(stopeMap.entries())
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value)

  const yAxisData = sortedData.map(item => item.name)
  const seriesData = sortedData.map(item => item.value)

  // 计算X轴最大值
  const maxValue = Math.max(...seriesData)
  const xMax = maxValue > 0 ? Math.ceil(maxValue * 1.1) : 100

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params: any) {
        const item = params[0]
        const value = item.value
        const seriesName = item.name

        return `${seriesName}<br/>
          <div style="display:flex;align-items:center;margin:5px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:#409EFF;border-radius:50%;margin-right:5px;"></span>
            出矿量: <strong>${value}吨</strong>
          </div>`
      },
    },
    grid: {
      left: '15%',
      right: '10%',
      top: '10%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'value',
        name: '出矿量(吨)',
        min: 0,
        max: xMax,
        interval: xMax / 5,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#409EFF',
          },
        },
        axisLabel: {
          formatter: '{value}',
        },
        nameTextStyle: {
          color: '#409EFF',
        },
      },
    ],
    yAxis: [
      {
        type: 'category',
        data: yAxisData,
        axisLine: {
          show: true,
        },
        axisTick: {
          show: true,
        },
        axisLabel: {
          formatter: function (value: string) {
            return value
          },
        },
      },
    ],
    series: [
      {
        name: '出矿量',
        type: 'bar',
        data: seriesData,
        itemStyle: {
          color: '#409EFF',
        },
        barWidth: '60%',
        label: {
          show: true,
          position: 'right',
          formatter: '{c}',
          color: '#666',
        },
      },
    ],
  }
})
</script>
