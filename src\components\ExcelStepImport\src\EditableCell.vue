<template>
  <div
    class="editable-cell"
    :class="{
      'editing': isEditing,
      'has-error': hasError,
      'has-warning': hasWarning,
      'editable': editable
    }"
    @click="handleClick"
    @mouseenter="showTooltip"
    @mouseleave="hideTooltip"
  >
    <!-- 编辑模式 -->
    <div v-if="isEditing" class="edit-container">
      <!-- 下拉选择器 -->
      <el-select
        v-if="hasOptions"
        ref="selectRef"
        v-model="editValue"
        size="small"
        :filterable="filterable"
        :allow-create="false"
        placeholder="请选择"
        @blur="handleBlur"
        @keyup.enter="handleSave"
        @keyup.esc="handleCancel"
        style="width: 100%"
      >
        <el-option
          v-for="option in options"
          :key="option.value"
          :label="option.label"
          :value="option.label"
        />
      </el-select>

      <!-- 普通输入框 -->
      <el-input
        v-else
        ref="inputRef"
        v-model="editValue"
        size="small"
        @blur="handleBlur"
        @keyup.enter="handleSave"
        @keyup.esc="handleCancel"
      />
    </div>
    
    <!-- 显示模式 -->
    <span v-else class="cell-content">
      {{ displayValue }}
    </span>

    <!-- 错误图标 -->
    <el-icon
      v-if="hasError || hasWarning"
      class="error-icon"
      :class="{ 'error': hasError, 'warning': hasWarning }"
      size="14"
    >
      <Warning />
    </el-icon>

    <!-- 错误提示 -->
    <div
      v-if="showErrorTooltip && (hasError || hasWarning)"
      class="error-tooltip"
      :style="tooltipStyle"
    >
      <div v-for="error in errors" :key="error.message" class="error-message">
        {{ error.message }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import type { TMessage } from '@/apis/model'

interface Props {
  value: any
  rowIndex: number
  fieldName: string
  fieldLabel: string
  errors: TMessage[]
  editable?: boolean
  // 下拉选项相关
  hasOptions?: boolean
  options?: Array<{ value: string; label: string }>
  filterable?: boolean
}

interface Emits {
  'update': [data: {
    rowIndex: number
    fieldName: string
    value: any
    oldValue: any
  }]
}

const props = withDefaults(defineProps<Props>(), {
  editable: true,
  hasOptions: false,
  options: () => [],
  filterable: true
})

const emit = defineEmits<Emits>()

// 响应式数据
const isEditing = ref(false)
const editValue = ref('')
const originalValue = ref('')
const showErrorTooltip = ref(false)
const tooltipStyle = ref({})
const inputRef = ref()
const selectRef = ref()

// 计算属性
const displayValue = computed(() => {
  const value = props.value ?? ''

  // 对于有选项的字段，直接显示值（现在存储的就是label）
  // 对于无选项的字段，也直接显示值
  return value
})

const hasError = computed(() => {
  return props.errors.some(error => error.type === 'ERROR')
})

const hasWarning = computed(() => {
  return props.errors.some(error => error.type === 'WARNING')
})

// 监听器
watch(() => props.value, (newValue) => {
  if (!isEditing.value) {
    editValue.value = newValue ?? ''
  }
}, { immediate: true })

// 方法
const handleClick = () => {
  if (!props.editable || isEditing.value) return
  
  startEdit()
}

const startEdit = async () => {
  isEditing.value = true
  originalValue.value = props.value

  // 对于有选项的字段，编辑值就是当前值（label）
  // 对于无选项的字段，编辑值也是当前值
  editValue.value = props.value ?? ''

  await nextTick()

  if (props.hasOptions) {
    // 如果是下拉选择器，聚焦并打开下拉
    selectRef.value?.focus()
  } else {
    // 如果是普通输入框，聚焦并选中文本
    inputRef.value?.focus()
    inputRef.value?.select()
  }
}

const handleBlur = () => {
  if (isEditing.value) {
    handleSave()
  }
}

const handleSave = () => {
  if (!isEditing.value) return
  
  const newValue = editValue.value
  const oldValue = originalValue.value
  
  if (newValue !== oldValue) {
    emit('update', {
      rowIndex: props.rowIndex,
      fieldName: props.fieldName,
      value: newValue,
      oldValue: oldValue
    })
  }
  
  isEditing.value = false
}

const handleCancel = () => {
  editValue.value = originalValue.value
  isEditing.value = false
}

const showTooltip = (event: MouseEvent) => {
  if (!hasError.value && !hasWarning.value) return
  
  const rect = (event.target as HTMLElement).getBoundingClientRect()
  tooltipStyle.value = {
    top: `${rect.bottom + 5}px`,
    left: `${rect.left}px`,
    position: 'fixed',
    zIndex: 9999
  }
  
  showErrorTooltip.value = true
}

const hideTooltip = () => {
  showErrorTooltip.value = false
}
</script>

<style scoped>
.editable-cell {
  position: relative;
  min-height: 32px;
  padding: 4px 8px;
  border: 2px solid transparent;
  border-radius: 4px;

  cursor: default;
  display: flex;
  align-items: center;

  &.editable {
    cursor: pointer;

    &:hover {
      background-color: #f0f4ff;
      border-color: var(--el-color-primary-light-7);
    }
  }

  &.editing {
    padding: 0;
    border-color: var(--el-color-primary);

    .edit-container {
      width: 100%;
      height: 100%;
    }
  }

  &.has-error {
    border-color: var(--el-color-error) !important;
    background-color: #fef2f2 !important;
  }

  &.has-warning {
    border-color: var(--el-color-warning) !important;
    background-color: #fffbf0 !important;
  }

  .cell-content {
    flex: 1;
    word-break: break-all;
    line-height: 1.4;
  }

  .error-icon {
    position: absolute;
    top: 2px;
    right: 2px;

    &.error {
      color: var(--el-color-error);
    }

    &.warning {
      color: var(--el-color-warning);
    }
  }

  .error-tooltip {
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    max-width: 200px;
    word-wrap: break-word;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    &::before {
      content: "";
      position: absolute;
      top: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-bottom: 5px solid #333;
    }

    .error-message {
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  :deep(.el-input) {
    .el-input__wrapper {
      box-shadow: none;
      border: none;
      background: transparent;
    }
  }

  :deep(.el-select) {
    .el-select__wrapper {
      box-shadow: none;
      border: none;
      background: transparent;
    }
  }
}
</style>
