<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="600px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsCrushing02a } from '@/apis/data'
import type { TDateCrushingOperationStats } from '@/apis/model'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDateCrushingOperationStats[]>([])
const lastViewType = ref('monthly')

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate,
    }

    const res = await apiDataStatsCrushing02a(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取运行时间数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 计算每日总时间（分钟）
const totalMinutes = computed(() => {
  return {
    daily: 24 * 60,
    weekly: 7 * 24 * 60,
    monthly: 30 * 24 * 60,
  }[props.dateRange.viewType] || 0
})

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) return undefined

  // 获取所有日期
  const dates = chartData.value.map(item => formatDisplayDate(item)).filter(Boolean) as string[]
  
  // 准备柱状图数据
  const barSeries = [
    {
      name: '运行时间',
      type: 'bar',
      stack: 'total',
      barWidth: '60%',
      emphasis: { focus: 'series' },
      data: chartData.value.map(item => item.totalOperationTime || 0),
      itemStyle: { color: '#5B9BD5' },
    },
    {
      name: '故障处理时间',
      type: 'bar',
      stack: 'total',
      barWidth: '60%',
      emphasis: { focus: 'series' },
      data: chartData.value.map(item => item.totalFaultTime || 0),
      itemStyle: { color: '#70AD47' },
    },
    {
      name: '闲置时间',
      type: 'bar',
      stack: 'total',
      barWidth: '60%',
      emphasis: { focus: 'series' },
      data: chartData.value.map(item => {
        const operationTime = Number(item.totalOperationTime) || 0
        const faultTime = Number(item.totalFaultTime) || 0
        return Math.max(0, totalMinutes.value - operationTime - faultTime)
      }),
      itemStyle: { color: '#FFC000' },
    }
  ]

  // 准备折线图数据 - 有效运行率
  const lineSeries = [
    {
      name: '有效运行率',
      type: 'line',
      yAxisIndex: 1,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: { color: '#E74C3C' },
      lineStyle: { width: 2, color: '#E74C3C' },
      data: chartData.value.map(item => {
        const operationTime = Number(item.totalOperationTime) || 0
        return totalMinutes.value > 0 ? Number((operationTime / totalMinutes.value * 100).toFixed(1)) : 0
      }),
    }
  ]

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      formatter: (params: any[]) => {
        const date = params[0].axisValue
        const operationTime = params.find(p => p.seriesName === '运行时间')?.value || 0
        const faultTime = params.find(p => p.seriesName === '故障处理时间')?.value || 0
        const idleTime = params.find(p => p.seriesName === '闲置时间')?.value || 0
        const efficiency = params.find(p => p.seriesName === '有效运行率')?.value || 0
        
        let result = `${date}<br/>`
        result += `<span style="color:#5B9BD5;">●</span> 运行时间: ${operationTime}分钟<br/>`
        result += `<span style="color:#70AD47;">●</span> 故障处理时间: ${faultTime}分钟<br/>`
        result += `<span style="color:#FFC000;">●</span> 闲置时间: ${idleTime}分钟<br/>`
        result += `<span style="color:#E74C3C;">●</span> 有效运行率: ${efficiency}%<br/>`
        
        return result
      }
    },
    legend: {
      data: ['运行时间', '故障处理时间', '闲置时间', '有效运行率'],
      bottom: 0,
      left: 'center',
      itemGap: 20,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 12,
        lineHeight: 16,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      top: '10%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: ['5%', '5%'],
        data: dates,
        axisLabel: {
          rotate: 0,
          margin: 15,
          overflow: 'truncate',
        },
        axisTick: {
          alignWithLabel: true,
        },
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '时间(分钟)',
        min: 0,
        max: totalMinutes.value,
        interval: totalMinutes.value / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#5B9BD5',
          },
        },
        nameTextStyle: {
          color: '#5B9BD5',
        },
      },
      {
        type: 'value',
        name: '有效运行率(%)',
        min: 0,
        max: 100,
        interval: 20,
        position: 'right',
        axisLabel: {
          formatter: '{value}%',
        },
        axisLine: {
          lineStyle: {
            color: '#E74C3C',
          },
        },
        nameTextStyle: {
          color: '#E74C3C',
        },
      }
    ],
    series: [...barSeries, ...lineSeries],
  }
})
</script>
