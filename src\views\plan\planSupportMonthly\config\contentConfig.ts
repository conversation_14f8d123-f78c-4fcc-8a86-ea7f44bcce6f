export const tableItem: BaseTableItem[] = [
  {
    prop: 'planDate',
    label: '计划月份',
  },
  {
    prop: 'projectDepartmentName',
    label: '项目部',
  },
  {
    prop: 'workingFaceName',
    label: '工作面',
  },
  {
    prop: 'stopeName',
    label: '采场',
  },
  {
    prop: 'boltMeshSupportMeter',
    label: '锚网支护米数',
  },
  {
    prop: 'boltMeshSupportVolume',
    label: '锚网支护方量',
  },
  {
    prop: 'shotcreteSupportMeter',
    label: '喷浆支护米数',
  },
  {
    prop: 'shotcreteSupportVolume',
    label: '喷浆支护方量',
  },
  {
    prop: 'supportVolume',
    label: '支护方量',
  },
  {
    prop: 'supportMeter',
    label: '支护米数',
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },
]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
