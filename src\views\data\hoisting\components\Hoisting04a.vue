<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsHoisting04a } from '@/apis/data'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<any[]>([])
const lastViewType = ref('monthly')

const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate,
    }

    const res = await apiDataStatsHoisting04a(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取提升斗数数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: [],
      },
      yAxis: [
        {
          type: 'value',
          name: '提升斗数(吨)',
          position: 'left',
          max: 250,
          interval: 50,
        },
        {
          type: 'value',
          name: '作业时间(分钟)',
          position: 'right',
          max: 250,
          interval: 50,
        },
      ],
      series: [],
    }
  }

  const xAxisData = chartData.value.map((item: any) => formatDisplayDate(item))
  const bucketData = chartData.value.map((item: any) => item.totalBuckets || 0)
  const timeData = chartData.value.map(
    (item: any) => Math.round(item.totalOperationTime) || 0
  ) // 转换为分钟
  
  // 计算提升效率 (分钟/斗)
  const efficiencyData = chartData.value.map((item: any) => {
    if (!item.totalBuckets || item.totalBuckets === 0) return 0
    return (item.totalOperationTime / item.totalBuckets).toFixed(2)
  })

  // 获取最大值函数
  const getMax = (arr: number[]) => {
    const max = Math.max(...arr)
    if (max <= 0) return 100 // 默认最大值
    return Math.ceil(max * 1.1) // 增加10%的余量
  }
  
  // 左右Y轴的最大值
  const leftMax = getMax([...bucketData, ...timeData])
  const rightMax = getMax(efficiencyData.map(Number))

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      formatter: function (params: any) {
        let result = params[0].axisValue + '<br/>'
        params.forEach((item: any) => {
          const value = item.value
          const seriesName = item.seriesName
          const color = item.color
          let unit = ''
          let displayValue = value

          if (seriesName === '提升斗数') {
            unit = ' 吨'
          } else if (seriesName === '作业时间') {
            unit = ' 分钟'
          } else if (seriesName === '提升效率') {
            unit = ' 分钟/斗'
            displayValue = Number(value).toFixed(2)
          }

          result += `<div style="display:flex;align-items:center;margin:5px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:${color};border-radius:50%;margin-right:5px;"></span>
            ${seriesName}: <strong>${displayValue}${unit}</strong>
          </div>`
        })
        return result
      },
    },
    legend: {
      data: ['提升斗数', '作业时间', '提升效率'],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: ['5%', '5%'],
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          formatter: function (value: string) {
            return value
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left',
        max: leftMax,
        interval: leftMax / 5,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#2f89cf',
          },
        },
        axisLabel: {
          formatter: '{value}',
        },
      },
      {
        type: 'value',
        name: '提升效率(分钟/斗)',
        position: 'right',
        max: rightMax,
        interval: rightMax / 5,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#67C23A',
          },
        },
        axisLabel: {
          formatter: '{value}',
        },
      },
    ],
    series: [
      {
        name: '提升斗数',
        type: 'bar',
        barGap: 0,
        barWidth: '40%',
        data: bucketData,
        itemStyle: {
          color: '#409EFF',
        },
      },
      {
        name: '作业时间',
        type: 'bar',
        barWidth: '40%',
        data: timeData,
        itemStyle: {
          color: '#36CFC9',
        },
      },
      {
        name: '提升效率',
        type: 'line',
        data: efficiencyData,
        yAxisIndex: 1,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#67C23A',
        },
        lineStyle: {
          color: '#67C23A',
          width: 3,
        },
      },
    ],
  }
})
</script>
