<template>
  <div class="w-full" v-if="!errorMessage">
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- 总故障次数雷达图 -->
      <div class="p-4 rounded-lg">
        <BaseEchart
          v-if="totalFaultOptions"
          :options="totalFaultOptions"
          height="400px"
        />
      </div>

      <!-- 0-8时时段雷达图 -->
      <div class="p-4 rounded-lg">
        <BaseEchart
          v-if="period1Options"
          :options="period1Options"
          height="400px"
        />
      </div>

      <!-- 8-20时时段雷达图 -->
      <div class="p-4 rounded-lg">
        <BaseEchart
          v-if="period2Options"
          :options="period2Options"
          height="400px"
        />
      </div>

      <!-- 20-0时时段雷达图 -->
      <div class="p-4 rounded-lg">
        <BaseEchart
          v-if="period3Options"
          :options="period3Options"
          height="400px"
        />
      </div>
    </div>
  </div>

  <div v-if="errorMessage" class="w-full flex items-center justify-center h-96">
    <el-alert
      :title="errorMessage"
      type="warning"
      :closable="false"
      show-icon
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { dayjs } from 'element-plus'
import { apiDataStatsCrushing03a } from '@/apis/data'
import type { TDateCrushingOperationPeriodStats } from '@/apis/model'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDateCrushingOperationPeriodStats[]>([])
const lastViewType = ref('monthly')
const errorMessage = ref('')

// 格式化日期显示
const formatDisplayDate = (item: TDateCrushingOperationPeriodStats) => {
  if (lastViewType.value === 'daily') {
    return dayjs(item.operationDate, 'YYYY-MM-DD').format('MM-DD')
  } else if (lastViewType.value === 'weekly') {
    return `${item.year}年${item.weekNumber}周`
  } else if (lastViewType.value === 'monthly') {
    return item.year && item.month ? `${item.month}月` : ''
  } else if (lastViewType.value === 'yearly') {
    return item.year ? `${item.year}年` : ''
  }
  return ''
}

// 验证日期跨度
const validateDateRange = () => {
  const { viewType, startDate, endDate } = props.dateRange
  if (!startDate || !endDate) {
    errorMessage.value = '请选择日期范围'
    return false
  }

  const start = dayjs(startDate)
  const end = dayjs(endDate)

  let periods = 0
  if (viewType === 'monthly') {
    periods = end.diff(start, 'month') + 1
  } else if (viewType === 'yearly') {
    periods = end.diff(start, 'year') + 1
  } else {
    errorMessage.value = '故障分析仅支持月度和年度视图'
    return false
  }

  if (periods < 3) {
    errorMessage.value = `${viewType === 'monthly' ? '月份' : '年份'}跨度不足3个，无法生成雷达图分析`
    return false
  }

  errorMessage.value = ''
  return true
}

// 生成完整的时间段数据
const generateCompleteTimeData = (
  startDate: string,
  endDate: string,
  viewType: string,
  originalData: TDateCrushingOperationPeriodStats[]
) => {
  const start = dayjs(startDate)
  const end = dayjs(endDate)
  const completeData: TDateCrushingOperationPeriodStats[] = []

  // 标准工作时段
  const standardPeriods = [
    { workingPeriodId: 1, workingPeriodName: '0-8时' },
    { workingPeriodId: 2, workingPeriodName: '8-20时' },
    { workingPeriodId: 3, workingPeriodName: '20-0时' },
  ]

  let current = start.clone()

  while (current.isBefore(end) || current.isSame(end)) {
    const year = current.year()
    const month = viewType === 'monthly' ? current.month() + 1 : undefined

    standardPeriods.forEach((period) => {
      // 查找原始数据中是否存在该时间段的数据
      const existingData = originalData.find(
        (item) =>
          item.year === year &&
          (viewType === 'yearly' || item.month === month) &&
          item.workingPeriodId === period.workingPeriodId
      )

      if (existingData) {
        // 如果存在数据，直接使用
        completeData.push(existingData)
      } else {
        // 如果不存在数据，创建默认数据
        completeData.push({
          year,
          month,
          weekNumber: undefined,
          operationDate: undefined,
          weekStartDate: undefined,
          weekEndDate: undefined,
          workingPeriodId: period.workingPeriodId,
          workingPeriodName: period.workingPeriodName,
          totalOperationTime: { source: '0.0', parsedValue: 0 },
          totalFaultTime: { source: '0.0', parsedValue: 0 },
          faultCount: 0,
          totalCrushingVolume: { source: '0.0', parsedValue: 0 },
        })
      }
    })

    // 移动到下一个时间段
    if (viewType === 'monthly') {
      current = current.add(1, 'month')
    } else {
      current = current.add(1, 'year')
    }
  }

  return completeData
}

// 获取图表数据
const fetchChartData = async () => {
  try {
    if (!validateDateRange()) {
      chartData.value = []
      return
    }

    const { viewType, startDate, endDate } = props.dateRange
    const params = {
      viewType,
      startDate,
      endDate,
    }

    const res = await apiDataStatsCrushing03a(params)
    const originalData = res || []

    // 生成完整的数据，填充缺失的时间段
    const completeData = generateCompleteTimeData(
      startDate,
      endDate,
      viewType,
      originalData
    )
    chartData.value = completeData
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取故障分析数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(
  () => props.dateRange,
  () => {
    if (props.dateRange?.startDate) {
      fetchChartData()
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

// 按时段分组数据
const groupedData = computed(() => {
  const groups: Record<string, Record<string, { faultCount?: number }>> = {}

  chartData.value.forEach((item: TDateCrushingOperationPeriodStats) => {
    const dateKey = formatDisplayDate(item)
    if (!dateKey) return

    if (!groups[dateKey]) {
      groups[dateKey] = {}
    }

    const period = item.workingPeriodName || '整体故障次数'
    groups[dateKey][period] = {
      faultCount: item.faultCount || 0,
    }
  })

  return groups
})

// 创建雷达图配置的通用函数
const createRadarOptions = (
  title: string,
  data: number[],
  dates: string[],
  color: string
) => {
  const maxValue = Math.max(...data, 10)

  return {
    title: {
      text: title,
      left: 'center',
      bottom: 0,
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return params.value.map((item: any, index: number) => {
          return `<div>${dates[index]}: ${item}次故障</div>`
        }).join('')
      },
    },
    radar: {
      indicator: dates.map((date) => ({
        name: date,
        max: maxValue + 5,
      })),
      center: ['50%', '55%'],
      radius: '65%',
      axisName: {
        color: '#333',
        fontSize: 10,
      },
      splitLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)'],
        },
      },
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: data,
            name: title,
            itemStyle: { color },
            lineStyle: { color, width: 2 },
            areaStyle: { color: color + '80' },
          },
        ],
      },
    ],
  }
}

// 总故障次数雷达图配置
const totalFaultOptions = computed(() => {
  if (!chartData.value || !chartData.value.length || errorMessage.value)
    return undefined

  const dates = Object.keys(groupedData.value).sort()
  if (dates.length < 3) return undefined

  // 计算每个日期的总故障次数
  const totalData = dates.map((date) => {
    const periods = groupedData.value[date]
    return Object.values(periods).reduce(
      (sum, period) => sum + (period.faultCount || 0),
      0
    )
  })

  return createRadarOptions('总故障次数', totalData, dates, '#5B9BD5')
})

// 0-8时时段雷达图配置
const period1Options = computed(() => {
  if (!chartData.value || !chartData.value.length || errorMessage.value)
    return undefined

  const dates = Object.keys(groupedData.value).sort()
  if (dates.length < 3) return undefined

  const data = dates.map(
    (date) => groupedData.value[date]?.['0-8时']?.faultCount || 0
  )
  return createRadarOptions('故障次数 (0-8时)', data, dates, '#5B9BD5')
})

// 8-20时时段雷达图配置
const period2Options = computed(() => {
  if (!chartData.value || !chartData.value.length || errorMessage.value)
    return undefined

  const dates = Object.keys(groupedData.value).sort()
  if (dates.length < 3) return undefined

  const data = dates.map(
    (date) => groupedData.value[date]?.['8-20时']?.faultCount || 0
  )
  return createRadarOptions('故障次数 (8-20时)', data, dates, '#70AD47')
})

// 20-0时时段雷达图配置
const period3Options = computed(() => {
  if (!chartData.value || !chartData.value.length || errorMessage.value)
    return undefined

  const dates = Object.keys(groupedData.value).sort()
  if (dates.length < 3) return undefined

  const data = dates.map(
    (date) => groupedData.value[date]?.['20-0时']?.faultCount || 0
  )
  return createRadarOptions('故障次数 (20-0时)', data, dates, '#FFC000')
})
</script>
