// 自动生成的 API 文件，请勿手动修改

import { request } from '@/utils/hsj/service/index'
import { TActualHoistPeriodStats, TActualHoistStats } from './model'

export async function apiActualHoistStatsVolumePeriod(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TActualHoistPeriodStats[]> {
  return request<{ data: TActualHoistPeriodStats[] }>({
    url: '/actual/hoist_stats/volume-period',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiActualHoistStatsVolumeAll(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TActualHoistStats[]> {
  return request<{ data: TActualHoistStats[] }>({
    url: '/actual/hoist_stats/volume-all',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiActualHoistStatsTimeAll(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TActualHoistStats[]> {
  return request<{ data: TActualHoistStats[] }>({
    url: '/actual/hoist_stats/time-all',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiActualHoistStatsPeriodOperationTime(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TActualHoistPeriodStats[]> {
  return request<{ data: TActualHoistPeriodStats[] }>({
    url: '/actual/hoist_stats/period-operation-time',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiActualHoistStatsFaultPeriod(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TActualHoistPeriodStats[]> {
  return request<{ data: TActualHoistPeriodStats[] }>({
    url: '/actual/hoist_stats/fault-period',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


export async function apiActualHoistStatsBucketAll(query: {
  viewType?: string;
  startDate?: string;
  endDate?: string;
}): Promise<TActualHoistStats[]> {
  return request<{ data: TActualHoistStats[] }>({
    url: '/actual/hoist_stats/bucket-all',
    method: 'get',
    params: query
  }).then(res => res.data); 
}


