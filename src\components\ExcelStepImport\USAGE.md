# ExcelStepImport 使用指南

## 🚀 快速开始

### 1. 基础使用

```vue
<template>
  <div>
    <el-button type="primary" @click="showImport = true">导入数据</el-button>

    <ExcelStepImport
      v-model:visible="showImport"
      template-key="your_template_key"
      title="数据导入"
      @success="handleSuccess"
      @error="handleError"
      @close="handleClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import ExcelStepImport from '@/components/ExcelStepImport'
import type { TExcelImportResultObject } from '@/apis/model'

const showImport = ref(false)

const handleSuccess = (result: TExcelImportResultObject) => {
  ElMessage.success(`导入成功！成功导入 ${result.success} 条记录`)
}

const handleError = (error: any) => {
  ElMessage.error('导入失败，请检查数据格式')
}

const handleClose = () => {
  showImport.value = false
}
</script>
```

### 2. 组件属性说明

| 属性 | 类型 | 默认值 | 必填 | 说明 |
|------|------|--------|------|------|
| v-model:visible | boolean | false | ✅ | 控制弹窗显示/隐藏 |
| template-key | string | - | ✅ | 后端模板标识 |
| title | string | 'Excel数据导入' | ❌ | 弹窗标题 |
| width | string/number | '1000px' | ❌ | 弹窗宽度 |
| max-file-size | number | 10 | ❌ | 最大文件大小(MB) |
| accept-types | string[] | ['.xlsx', '.xls'] | ❌ | 允许的文件类型 |

### 3. 事件说明

| 事件 | 参数 | 说明 |
|------|------|------|
| @success | (result: TExcelImportResultObject) | 导入成功 |
| @error | (error: any) | 导入失败 |
| @close | - | 弹窗关闭 |
| @cancel | - | 用户取消导入 |

### 2. 在表格页面中集成

```vue
<template>
  <div class="table-page">
    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button type="success" @click="openImport">批量导入</el-button>
      <el-button type="warning" @click="handleExport">导出</el-button>
    </div>

    <!-- 数据表格 -->
    <el-table :data="tableData" border>
      <!-- 表格列定义 -->
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      @current-change="handlePageChange"
    />

    <!-- 导入组件 -->
    <ExcelStepImport
      v-model:visible="importVisible"
      template-key="user_data"
      title="用户数据批量导入"
      @success="handleImportSuccess"
      @error="handleImportError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ExcelStepImport from '@/components/ExcelStepImport'

const importVisible = ref(false)
const tableData = ref([])
const pagination = ref({
  page: 1,
  size: 10,
  total: 0
})

const openImport = () => {
  importVisible.value = true
}

const handleImportSuccess = (result) => {
  ElMessage.success(`导入完成！成功 ${result.success} 条，失败 ${result.error} 条`)
  // 重新加载表格数据
  loadTableData()
}

const loadTableData = async () => {
  // 加载表格数据的逻辑
}

onMounted(() => {
  loadTableData()
})
</script>
```

### 3. 多模板选择

```vue
<template>
  <div class="multi-template-import">
    <el-select v-model="selectedTemplate" placeholder="选择导入模板">
      <el-option
        v-for="template in templates"
        :key="template.key"
        :label="template.name"
        :value="template.key"
      />
    </el-select>
    
    <el-button 
      type="primary" 
      @click="openImport"
      :disabled="!selectedTemplate"
    >
      开始导入
    </el-button>

    <ExcelStepImport
      v-model:visible="importVisible"
      :template-key="selectedTemplate"
      :title="selectedTemplateName"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ExcelStepImport from '@/components/ExcelStepImport'

const selectedTemplate = ref('')
const importVisible = ref(false)

const templates = ref([
  { key: 'user_import', name: '用户数据导入' },
  { key: 'product_import', name: '产品数据导入' },
  { key: 'order_import', name: '订单数据导入' }
])

const selectedTemplateName = computed(() => {
  const template = templates.value.find(t => t.key === selectedTemplate.value)
  return template ? `${template.name}` : '数据导入'
})

const openImport = () => {
  importVisible.value = true
}

const handleImportSuccess = (result) => {
  // 处理导入成功
}
</script>
```

## 高级配置

### 1. 自定义文件限制

```vue
<ExcelStepImport
  v-model:visible="importVisible"
  template-key="large_data"
  title="大数据导入"
  :max-file-size="50"
  :accept-types="['.xlsx', '.xls', '.csv']"
  width="1400px"
/>
```

### 2. 自动导入模式

```vue
<ExcelStepImport
  v-model:visible="importVisible"
  template-key="auto_import"
  :auto-import="true"
  @success="handleAutoImportSuccess"
/>
```

### 3. 监听所有事件

```vue
<ExcelStepImport
  v-model:visible="importVisible"
  template-key="template_key"
  @success="handleSuccess"
  @error="handleError"
  @close="handleClose"
  @cancel="handleCancel"
/>

<script setup>
const handleSuccess = (result) => {
  console.log('Import successful:', result)
}

const handleError = (error) => {
  console.error('Import failed:', error)
}

const handleClose = () => {
  console.log('Dialog closed')
}

const handleCancel = () => {
  console.log('Import cancelled')
}
</script>
```

## 样式定制

### 1. 覆盖CSS变量

```css
.custom-import-dialog .excel-step-import-dialog {
  --el-color-primary: #ff6b6b;
  --el-color-success: #51cf66;
  --el-color-warning: #ffd43b;
  --el-color-error: #ff6b6b;
}
```

### 2. 自定义步骤样式

```css
.excel-step-import-dialog .step-indicator .step {
  /* 自定义步骤样式 */
}

.excel-step-import-dialog .upload-area {
  /* 自定义上传区域样式 */
}
```

## 错误处理

### 1. 网络错误处理

```vue
<script setup>
const handleImportError = (error) => {
  if (error.code === 'NETWORK_ERROR') {
    ElMessage.error('网络连接失败，请检查网络后重试')
  } else if (error.code === 'FILE_TOO_LARGE') {
    ElMessage.error('文件过大，请选择小于10MB的文件')
  } else {
    ElMessage.error('导入失败，请联系管理员')
  }
}
</script>
```

### 2. 数据验证错误处理

```vue
<script setup>
const handleImportSuccess = (result) => {
  if (result.error > 0) {
    ElMessageBox.confirm(
      `导入完成，但有 ${result.error} 条数据失败。是否下载失败数据？`,
      '导入结果',
      {
        confirmButtonText: '下载失败数据',
        cancelButtonText: '忽略',
        type: 'warning'
      }
    ).then(() => {
      // 下载失败数据的逻辑
    })
  } else {
    ElMessage.success('所有数据导入成功！')
  }
}
</script>
```

## 最佳实践

1. **模板管理**: 为不同的业务场景创建不同的导入模板
2. **错误处理**: 提供清晰的错误信息和解决方案
3. **用户体验**: 在导入完成后刷新相关数据
4. **性能优化**: 对于大文件导入，考虑分批处理
5. **权限控制**: 根据用户权限显示不同的导入选项

## 常见问题

### Q: 如何自定义模板下载的文件名？
A: 文件名由后端API返回的Content-Disposition头决定，前端会自动解析。

### Q: 如何处理大文件导入？
A: 可以通过调整maxFileSize属性和后端配置来支持更大的文件。

### Q: 如何添加自定义验证规则？
A: 验证规则在后端模板配置中定义，前端组件会自动应用这些规则。
