<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="600px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsHoisting02a } from '@/apis/data'
import type { TActualHoistStats } from '@/apis/model'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TActualHoistStats[]>([])
const lastViewType = ref('daily')

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate,
    }

    const res = await apiDataStatsHoisting02a(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取运行时间数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 计算每日总时间（分钟）
const totalMinutes = computed(() => {
  return {
    daily: 24 * 60,
    weekly: 7 * 24 * 60,
    monthly: 30 * 24 * 60,
  }[props.dateRange.viewType] || 0
})

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) return undefined

  // 获取所有日期
  const dates = chartData.value.map(item => formatDisplayDate(item)).filter(Boolean) as string[]
  
  // 准备柱状图数据
  const barSeries = [
    {
      name: '运行时间',
      type: 'bar',
      stack: 'total',
      barWidth: '60%',
      emphasis: { focus: 'series' },
      data: chartData.value.map(item => item.totalOperationTime || 0),
      itemStyle: { color: '#409EFF' },
      label: {
        show: true,
        position: 'inside',
        formatter: (params: any) => params.value > 0 ? params.value : '',
        color: '#fff',
        fontSize: 10,
        fontWeight: 'bold',
      }
    },
    {
      name: '故障处理时间',
      type: 'bar',
      stack: 'total',
      barWidth: '60%',
      emphasis: { focus: 'series' },
      data: chartData.value.map(item => item.totalFaultTime || 0),
      itemStyle: { color: '#36CFC9' },
      label: {
        show: true,
        position: 'inside',
        formatter: (params: any) => params.value > 0 ? params.value : '',
        color: '#fff',
        fontSize: 10,
        fontWeight: 'bold',
      }
    },
    {
      name: '闲置时间',
      type: 'bar',
      stack: 'total',
      barWidth: '60%',
      emphasis: { focus: 'series' },
      data: chartData.value.map(item => {
        const operationTime = Number(item.totalOperationTime) || 0
        const faultTime = Number(item.totalFaultTime) || 0
        return Math.max(0, totalMinutes.value - operationTime - faultTime)
      }),
      itemStyle: { color: '#95DE64' },
      label: {
        show: true,
        position: 'inside',
        formatter: (params: any) => params.value > 0 ? params.value : '',
        color: '#fff',
        fontSize: 10,
        fontWeight: 'bold',
      }
    }
  ]

  // 准备折线图数据
  const lineSeries = [
    {
      name: '运行率',
      type: 'line',
      yAxisIndex: 1,
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: { color: '#FF9F7F' },
      lineStyle: { width: 3 },
      data: chartData.value.map(item => {
        const operationTime = Number(item.totalOperationTime) || 0
        return totalMinutes.value > 0 ? (operationTime / totalMinutes.value * 100).toFixed(1) : 0
      })
    },
    {
      name: '故障处置率',
      type: 'line',
      yAxisIndex: 1,
      symbol: 'diamond',
      symbolSize: 8,
      itemStyle: { color: '#FF6B81' },
      lineStyle: { width: 3 },
      data: chartData.value.map(item => {
        const faultTime = Number(item.totalFaultTime) || 0
        return totalMinutes.value > 0 ? (faultTime / totalMinutes.value * 100).toFixed(1) : 0
      })
    },
    {
      name: '闲置率',
      type: 'line',
      yAxisIndex: 1,
      symbol: 'triangle',
      symbolSize: 8,
      itemStyle: { color: '#A8E6CF' },
      lineStyle: { width: 3 },
      data: chartData.value.map(item => {
        const operationTime = Number(item.totalOperationTime) || 0
        const faultTime = Number(item.totalFaultTime) || 0
        const idleTime = Math.max(0, totalMinutes.value - operationTime - faultTime)
        return totalMinutes.value > 0 ? (idleTime / totalMinutes.value * 100).toFixed(1) : 0
      })
    }
  ]

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params: any[]) => {
        const date = params[0].axisValue
        const operationTime = params.find(p => p.seriesName === '运行时间')?.value || 0
        const faultTime = params.find(p => p.seriesName === '故障处理时间')?.value || 0
        const idleTime = params.find(p => p.seriesName === '闲置时间')?.value || 0
        
        let result = `${date}<br/>`
        result += `${params[0].marker} 运行时间: ${operationTime}分钟 (${(operationTime / totalMinutes.value * 100).toFixed(1)}%)<br/>`
        result += `${params[1].marker} 故障处理时间: ${faultTime}分钟 (${(faultTime / totalMinutes.value * 100).toFixed(1)}%)<br/>`
        result += `${params[2].marker} 闲置时间: ${idleTime}分钟 (${(idleTime / totalMinutes.value * 100).toFixed(1)}%)<br/>`
        
        return result
      }
    },
    legend: {
      data: ['运行时间', '故障处理时间', '闲置时间', '运行率', '故障处置率', '闲置率'],
      bottom: 0,
      left: 'center',
      itemGap: 15,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 12,
        lineHeight: 16,
      },
    },
    grid: {
      left: '3%',
      right: '3%',
      top: '10%',
      bottom: '18%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: ['5%', '5%'],
        data: dates,
        axisLabel: {
          rotate: 0,
          margin: 15,
          overflow: 'truncate',
        },
        axisTick: {
          alignWithLabel: true,
        },
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '时间(分钟)',
        min: 0,
        max: totalMinutes.value,
        interval: totalMinutes.value / 5,
        axisLabel: {
          formatter: '{value}',
        },
      },
      {
        type: 'value',
        name: '百分比(%)',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          formatter: '{value}%',
        },
      }
    ],
    series: [...barSeries, ...lineSeries],
  }
})
</script>
