<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="500px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsHoisting01b } from '@/apis/data'
import type { TActualHoistPeriodStats } from '@/apis/model'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TActualHoistPeriodStats[]>([])
const lastViewType = ref('daily')

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate,
    }

    const res = await apiDataStatsHoisting01b(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取提升量数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

// 按日期和时段分组数据
const groupedData = computed(() => {
  const groups: Record<
    string,
    Record<string, { buckets?: number; weight?: any }>
  > = {}

  chartData.value.forEach((item: TActualHoistPeriodStats) => {
    const dateKey = formatDisplayDate(item)

    if (!dateKey) return

    if (!groups[dateKey]) {
      groups[dateKey] = {}
    }

    const period = item.workingPeriodName || '未知时段'
    groups[dateKey][period] = {
      buckets: item.totalBuckets,
      weight: item.totalWeight,
    }
  })

  return groups
})

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: [],
      },
      yAxis: {
        type: 'value',
      },
      series: [],
    }
  }

  // 获取所有唯一的日期
  const dates = Object.keys(groupedData.value).sort()

  const periods = Array.from(
    new Set(
      Object.values(groupedData.value).flatMap((periods) =>
        Object.keys(periods)
      )
    )
  ).sort((a, b) => {
    const startA = Number(a.split('-')[0])
    const startB = Number(b.split('-')[0])
    return startA - startB
  })

  // 获取所有时段
  const periodAndTypes = periods.reduce((acc, period) => {
    acc.push(period + '提升量')
    acc.push(period + '斗数')
    return acc
  }, [] as string[])

  let y0Max = 0
  let y1Max = 0

  const dataMap = periodAndTypes.reduce(
    (acc, periodAndType) => {
      const isWeight = periodAndType.endsWith('提升量')
      const period = periodAndType.replace('提升量', '').replace('斗数', '')
      acc[periodAndType] = dates.map((date) => {
        const periodData = groupedData.value[date][period]
        if (!periodData) return 0
        return isWeight ? periodData.weight || 0 : periodData.buckets || 0
      })

      if (isWeight) {
        y0Max = Math.max(y0Max, ...acc[periodAndType])
      } else {
        y1Max = Math.max(y1Max, ...acc[periodAndType])
      }
      return acc
    },
    {} as Record<string, number[]>
  )

  const getMax = (max: number) => {
    max = Math.round(max)
    return (
      (Number(max.toString()[0]) + 1) * Math.pow(10, max.toString().length - 1)
    )
  }
  y0Max = getMax(y0Max)
  y1Max = getMax(y1Max)

  // 生成系列数据
  const series = periodAndTypes.flatMap((periodAndType) => {
    const isWeight = periodAndType.endsWith('提升量')
    const yAxisIndex = isWeight ? 0 : 1
    const type = isWeight ? 'bar' : 'line'
    const itemStyle = {
      color: getColorByPeriod(periodAndType),
    }

    const lineStyle = {
      color: getColorByPeriod(periodAndType),
      width: 2,
    }

    const data = dataMap[periodAndType]

    return {
      name: periodAndType,
      type,
      yAxisIndex,
      data,
      itemStyle,
      ...(type === 'line'
        ? {
            symbol: 'circle',
            symbolSize: 8,
            lineStyle,
          }
        : {
            barGap: 0,
            barWidth: '25%',
          }),
    }
  })

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: (params: any) => {
        const date = params[0].axisValue

        const periodData = groupedData.value[date] || {}
        const dataMap = periodAndTypes.reduce((acc, periodAndType) => {
          const isWeight = periodAndType.endsWith('提升量')
          const period = periodAndType.replace('提升量', '').replace('斗数', '')
          const value = periodData[period] ? (periodData[period][isWeight ? 'weight' : 'buckets'] || 0) : 0
          const unit = isWeight ? '吨' : '斗'
          acc[periodAndType] = { value, unit }
          return acc
        }, {} as Record<string, { value: number, unit: string }>)

        const keyValues = Object.entries(dataMap).map(([key, item]) => ({
          key,
          value: item.value,
          unit: item.unit
        }))

        let result = `<div style="margin-bottom: 5px;">${date}</div>`

        // 按时段分组显示
        const periods = Array.from(new Set(keyValues.map(item => item.key.replace('提升量', '').replace('斗数', ''))))

        periods.forEach(period => {
          const weightItem = keyValues.find(item => item.key === `${period}提升量`)
          const bucketItem = keyValues.find(item => item.key === `${period}斗数`)

          if (weightItem || bucketItem) {
            result += `<div style="margin: 5px 0;">
              <div style="font-weight: bold;">${period}</div>
              ${weightItem ? `<div>提升量: ${weightItem.value}${weightItem.unit}</div>` : ''}
              ${bucketItem ? `<div>斗数: ${bucketItem.value}${bucketItem.unit}</div>` : ''}
            </div>`
          }
        })

        return result
      },
    },
    legend: {
      data: periodAndTypes,
      bottom: 0,
      left: 'center',
      itemGap: 20,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 12,
        lineHeight: 16,
      },
    },
    grid: {
      left: '3%',
      right: '3%',
      top: '10%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: ['5%', '5%'],
      data: dates,
      axisLabel: {
        rotate: 0,
        margin: 15,
        overflow: 'truncate',
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '提升量(吨)',
        min: 0,
        max: y0Max,
        interval: y0Max / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#409EFF',
          },
        },
        nameTextStyle: {
          color: '#409EFF',
        },
      },
      {
        type: 'value',
        name: '斗数(斗)',
        min: 0,
        max: y1Max,
        interval: y1Max / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#FF4D4F',
          },
        },
        nameTextStyle: {
          color: '#FF4D4F',
        },
      },
    ],
    series,
  }
})

// 根据时段获取颜色
const getColorByPeriod = (period: string) => {
  const colorMap: Record<string, string> = {
    '0-8时提升量': '#409EFF',
    '8-20时提升量': '#36CFC9',
    '20-0时提升量': '#95DE64',
    '0-8时斗数': '#FF4D4F',
    '8-20时斗数': '#FAAD14',
    '20-0时斗数': '#52C41A',
  }

  return colorMap[period] || '#409EFF' // 默认颜色
}
</script>
