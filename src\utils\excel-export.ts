import $ from 'jquery'
import * as ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'

/**
 * 表格导出配置接口
 */
export interface TableExportConfig {
  /** 报表标题 */
  title?: string
  /** 文件名（不包含扩展名） */
  filename?: string
  /** 列宽设置 */
  columnWidth?: number
}

/**
 * 单元格数据接口
 */
export interface CellData {
  /** 单元格显示值 */
  value: string
  /** 原始值（未处理的） */
  rawValue: string
  /** 列合并数 */
  colspan: number
  /** 行合并数 */
  rowspan: number
  /** 是否隐藏 */
  hidden: boolean
  /** 是否为表头单元格 */
  isHeader: boolean
  /** 单元格类型 */
  type: 'text' | 'number' | 'percentage' | 'currency' | 'date' | 'empty'
  /** CSS类名 */
  className?: string
  /** 内联样式 */
  style?: string
}

/**
 * 行数据接口
 */
export interface RowData {
  /** 行中的单元格数据 */
  cells: CellData[]
  /** 是否为表头行 */
  isHeaderRow: boolean
  /** 行索引 */
  rowIndex: number
  /** 行类型 */
  rowType: 'thead' | 'tbody' | 'tfoot'
}

/**
 * 表格结构数据接口
 */
export interface TableStructure {
  /** 所有行数据 */
  rows: RowData[]
  /** 表头行数量 */
  headerRowCount: number
  /** 最大列数 */
  maxColumns: number
  /** 合并单元格信息 */
  mergedCells: Array<{
    startRow: number
    endRow: number
    startCol: number
    endCol: number
  }>
  /** 表格标题（如果有caption） */
  caption?: string
}

function mergeTables(container: HTMLElement, title: string, columnWidth: number = 15) {
  const tables = $(container).find('table').clone()
  const trList: { wrapperTag: string, html: string }[] = []
  let maxColumns = 0
  tables.each((_index, table) => {
    $(table).find('.hidden-cell').remove()
    $(table).find('tr').each((_index, tr) => {
      let allColspan = 0
      const thList = $(tr).find('th')
      const tdList = $(tr).find('td')
      if (thList.length > 0) {
        let firstRowSpan = thList.first()?.attr('rowspan')
        const allThRowSpanSameValue = thList.map((_index, th) => {
          return $(th).attr('rowspan')
          
        }).get().every((value) => value === firstRowSpan)
        thList.each((_index, th) => {
          $(th).css('font-weight', 'bold')
          $(th).css('background-color', '#FEC000')
          if (allThRowSpanSameValue) {
            $(th).attr('rowspan', '1')
          }
          const colspan = parseInt($(th).attr('colspan') || '1')
          allColspan += colspan
          // $(th).css('width', colspan * columnWidth * 12 + 'px')
        })
        trList.push({ wrapperTag: 'thead', html: `<tr>${$(tr).html()}</tr>` })
      } else {
        tdList.each((_index, td) => {
          $(td).removeAttr('width')
          const colspan = parseInt($(td).attr('colspan') || '1')
          $(td).css('width', colspan * columnWidth * 12 + 'px')
          allColspan += colspan
        })
        trList.push({ wrapperTag: 'tbody', html: `<tr>${$(tr).html()}</tr>` })
      }
      maxColumns = Math.max(maxColumns, allColspan)
    })
  })

  let tableHtml = ''
  let lastTag = ''
  trList.forEach(tr => {
    if (lastTag !== tr.wrapperTag) {
      if (lastTag) {
        tableHtml += `</${lastTag}>`
      }
      tableHtml += `<${tr.wrapperTag}>`
    }
    tableHtml += tr.html
    lastTag = tr.wrapperTag
  })
  if (lastTag) {
    tableHtml += `</${lastTag}>`
  }


  tableHtml = `<table border="1" cellpadding="2" cellspacing="0">
  <tbody>
  <tr>
  <td colspan="${maxColumns}" style="text-align: center; font-weight: bold; background-color: #92D050">${title}</td>
  </tr>
  </tbody>
${tableHtml}
</table>`

  return tableHtml
}

/**
 * 导出多个表格到Excel
 * @param container 包含表格的容器元素
 * @param config 导出配置
 */
export async function exportTablesToExcel(
  container: HTMLElement,
  config: TableExportConfig = {}
): Promise<void> {
  const {
    title = '报表数据',
    filename = '导出数据',
    columnWidth = 15
  } = config

  try {
    // 查找所有表格
    const tableHtml = mergeTables(container, title, columnWidth)

    navigator.clipboard.writeText(tableHtml)
    
    // 解析HTML表格并转换为Excel
    await convertHtmlTableToExcel(tableHtml, filename, columnWidth)
    
  } catch (error) {
    console.error('导出Excel失败:', error)
    throw error
  }
}

/**
 * 解析HTML表格样式
 */
function parseTableStyle(element: JQuery<HTMLElement>) {
  const style = element.attr('style') || ''
  const className = element.attr('class') || ''
  
  const styleObj: any = {
    font: {},
    fill: {},
    border: {},
    alignment: {}
  }
  
  // 解析内联样式
  if (style) {
    const styles = style.split(';').filter(s => s.trim())
    styles.forEach(s => {
      const [prop, value] = s.split(':').map(v => v.trim())
      if (!prop || !value) return
      
      switch (prop.toLowerCase()) {
        case 'background-color':
        case 'background':
          if (value !== 'transparent' && value !== 'inherit') {
            styleObj.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: convertColorToArgb(value) }
            }
          }
          break
        case 'color':
          styleObj.font.color = { argb: convertColorToArgb(value) }
          break
        case 'font-weight':
          if (value === 'bold' || parseInt(value) >= 600) {
            styleObj.font.bold = true
          }
          break
        case 'font-size':
          const size = parseInt(value)
          if (size) {
            styleObj.font.size = size
          }
          break
        case 'text-align':
          styleObj.alignment.horizontal = value as any
          break
        case 'vertical-align':
          styleObj.alignment.vertical = value === 'middle' ? 'middle' : value as any
          break
      }
    })
  }
  
  // 处理类名样式（如果需要）
  if (className.includes('text-center')) {
    styleObj.alignment.horizontal = 'center'
  }
  if (className.includes('text-right')) {
    styleObj.alignment.horizontal = 'right'
  }
  
  return styleObj
}

/**
 * 转换颜色值为ARGB格式
 */
function convertColorToArgb(color: string): string {
  if (!color) return 'FF000000'
  
  // 移除空格
  color = color.trim()
  
  // 处理十六进制颜色
  if (color.startsWith('#')) {
    const hex = color.substring(1)
    if (hex.length === 3) {
      // #RGB -> #RRGGBB
      const r = hex[0] + hex[0]
      const g = hex[1] + hex[1]
      const b = hex[2] + hex[2]
      return 'FF' + r + g + b
    } else if (hex.length === 6) {
      return 'FF' + hex.toUpperCase()
    }
  }
  
  // 处理RGB颜色
  if (color.startsWith('rgb(')) {
    const match = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
    if (match) {
      const r = parseInt(match[1]).toString(16).padStart(2, '0')
      const g = parseInt(match[2]).toString(16).padStart(2, '0')
      const b = parseInt(match[3]).toString(16).padStart(2, '0')
      return 'FF' + r + g + b
    }
  }
  
  // 处理常见颜色名称
  const colorMap: { [key: string]: string } = {
    'red': 'FFFF0000',
    'green': 'FF00FF00',
    'blue': 'FF0000FF',
    'yellow': 'FFFFFF00',
    'black': 'FF000000',
    'white': 'FFFFFFFF',
    'gray': 'FF808080',
    'grey': 'FF808080'
  }
  
  return colorMap[color.toLowerCase()] || 'FF000000'
}

/**
 * 将HTML表格转换为Excel文件
 */
async function convertHtmlTableToExcel(tableHtml: string, filename: string, columnWidth: number) {
  // 创建工作簿
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('Sheet1')
  
  // 解析HTML
  const $table = $(tableHtml)
  
  // 存储合并单元格信息
  const mergedCells: Array<{
    startRow: number
    endRow: number
    startCol: number
    endCol: number
  }> = []
  
  let currentRow = 1
  
  // 处理所有行
  $table.find('tr').each((_, tr) => {
    const $tr = $(tr)
    let currentCol = 1
    
    // 设置行高
    const $firstCell = $tr.find('td, th').first()
    console.log()
    const rowHeight = ($firstCell.outerHeight() || 0) * 2 || 24 // 获取行高，默认24
    const row = worksheet.getRow(currentRow)
    row.height = rowHeight
    
    // 跳过已被合并占用的单元格
    while (isCellMerged(currentRow, currentCol, mergedCells)) {
      currentCol++
    }
    
    $tr.find('td, th').each((_, cell) => {
      const $cell = $(cell)
      
      // 跳过已被合并占用的单元格
      while (isCellMerged(currentRow, currentCol, mergedCells)) {
        currentCol++
      }
      
      // 获取单元格内容
      const cellText = $cell.text().trim()
      
      // 获取合并信息
      const colspan = parseInt($cell.attr('colspan') || '1')
      const rowspan = parseInt($cell.attr('rowspan') || '1')
      
      // 设置单元格值
      const excelCell = worksheet.getCell(currentRow, currentCol)
      excelCell.value = cellText
      
      // 应用样式
      const cellStyle = parseTableStyle($cell)
      
      // 设置字体
      if (cellStyle.font && Object.keys(cellStyle.font).length > 0) {
        excelCell.font = { ...excelCell.font, ...cellStyle.font }
      }
      
      // 设置填充
      if (cellStyle.fill && Object.keys(cellStyle.fill).length > 0) {
        excelCell.fill = cellStyle.fill
      }
      
      // 设置对齐
      if (cellStyle.alignment && Object.keys(cellStyle.alignment).length > 0) {
        excelCell.alignment = { ...excelCell.alignment, ...cellStyle.alignment }
      }
      
      // 设置边框
      excelCell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
      
      // 处理合并单元格
      if (colspan > 1 || rowspan > 1) {
        const endRow = currentRow + rowspan - 1
        const endCol = currentCol + colspan - 1
        
        // 记录合并信息
        mergedCells.push({
          startRow: currentRow,
          endRow: endRow,
          startCol: currentCol,
          endCol: endCol
        })
        
        // 执行合并
        worksheet.mergeCells(currentRow, currentCol, endRow, endCol)
        
        // 为合并区域的所有单元格应用相同样式
        for (let r = currentRow; r <= endRow; r++) {
          for (let c = currentCol; c <= endCol; c++) {
            const mergedCell = worksheet.getCell(r, c)
            if (cellStyle.font && Object.keys(cellStyle.font).length > 0) {
              mergedCell.font = { ...mergedCell.font, ...cellStyle.font }
            }
            if (cellStyle.fill && Object.keys(cellStyle.fill).length > 0) {
              mergedCell.fill = cellStyle.fill
            }
            if (cellStyle.alignment && Object.keys(cellStyle.alignment).length > 0) {
              mergedCell.alignment = { ...mergedCell.alignment, ...cellStyle.alignment }
            }
            mergedCell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            }
          }
        }
      }
      
      currentCol += colspan
    })
    
    currentRow++
  })
  
  // 自动调整列宽
  worksheet.columns.forEach((column) => {
    column.width = columnWidth
    // let maxLength = 0
    // column.eachCell?.({ includeEmpty: true }, (cell) => {
    //   const cellLength = cell.value ? cell.value.toString().length : 0
    //   if (cellLength > maxLength) {
    //     maxLength = cellLength
    //   }
    // })
    // 设置最小和最大列宽，并考虑内容长度
    // column.width = Math.min(Math.max(maxLength + 2, 10), 50)
    
    // 设置自动换行
    // column.eachCell?.({ includeEmpty: true }, (cell) => {
    //   if (cell.value && cell.value.toString().length > 30) {
    //     cell.alignment = cell.alignment || {}
    //     cell.alignment.wrapText = true
    //   }
    // })
  })
  
  // 生成Excel文件并下载
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  })
  saveAs(blob, `${filename}.xlsx`)
}

/**
 * 检查单元格是否已被合并占用
 */
function isCellMerged(
  row: number, 
  col: number, 
  mergedCells: Array<{
    startRow: number
    endRow: number
    startCol: number
    endCol: number
  }>
): boolean {
  return mergedCells.some(merged => 
    row >= merged.startRow && row <= merged.endRow &&
    col >= merged.startCol && col <= merged.endCol &&
    !(row === merged.startRow && col === merged.startCol) // 排除起始单元格
  )
}
