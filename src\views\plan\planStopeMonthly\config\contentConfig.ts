export const tableItem: BaseTableItem[] = [
  {
    prop: 'planDate',
    label: '计划月份',
  },
  {
    prop: 'stopeName',
    label: '采场',
  },
  {
    prop: 'oreOutput',
    label: '出矿量',
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },
]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
