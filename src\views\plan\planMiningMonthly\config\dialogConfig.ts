export default (): BaseFormProps => {
  return {
    itemStyle: {
      padding: '0px 0px 0px 0px',
    },
    rules: {},
    formItems: [
      {
        field: 'planDate',
        config: {
          clearable: false,
          type: 'month',
          valueFormat: 'YYYYMM',
          format: 'YYYY-MM',
        },
        type: 'datepicker',
        label: '计划月份',
      },
      {
        field: 'projectDepartmentId',
        type: 'select',
        options: [],
        label: '项目部',
      },
      {
        field: 'driftMeter',
        type: 'input',
        label: '掘进米数',
      },
      {
        field: 'rawOreVolume',
        type: 'input',
        label: '原矿量',
      },
      {
        field: 'supportMeter',
        type: 'input',
        label: '支护米数',
      },
      {
        field: 'fillingVolume',
        type: 'input',
        label: '充填量',
      },
      {
        field: 'dthMeter',
        type: 'input',
        label: '潜孔米数',
      },
      {
        field: 'deepHoleMeter',
        type: 'input',
        label: '中深孔米数',
      },
      {
        field: 'oreOutputVolume',
        type: 'input',
        label: '出矿量',
      },
    ],
    colLayout: {
      xl: 24,
      lg: 24,
      md: 24,
      sm: 24,
      xs: 24,
    },
    elFormConfig: {
      labelWidth: '100px',
    },
  }
}
