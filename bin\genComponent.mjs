import fs from 'fs';
import path from 'path';

const rootDir = path.join('.', 'src', 'components');
const files = fs.readdirSync(rootDir);
const components = files.filter((file) => {
  return fs.statSync(path.join(rootDir, file)).isDirectory();
});

const newComponents = [];
for (const component of components) {
  const componentPath = path.join(rootDir, component, 'index.ts');
  if (!fs.existsSync(componentPath)) {
    const content = `import { App } from 'vue';
import ${component}Component from './${component}.vue';

const ${component} = {
  install: function(Vue: App) {
    Vue.component('${component}', ${component}Component);
  },
};

declare module 'vue' {
  interface GlobalComponents {
    ${component}: typeof ${component}Component;
  }
}

export default ${component};
`;
    fs.writeFileSync(componentPath, content);
    newComponents.push(component);
  }
}

const imports = newComponents
  .map((component) => {
    return `import ${component} from './components//${component}';`;
  })
  .join('\n');

const install =
  'App' +
  newComponents
    .map((component) => {
      return `.use(${component})`;
    })
    .join('\n') +
  ';';

console.log(imports);
console.log(install);
