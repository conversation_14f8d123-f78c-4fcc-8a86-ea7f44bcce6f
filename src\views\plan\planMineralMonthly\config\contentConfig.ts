export const tableItem: BaseTableItem[] = [
  {
    prop: 'planDate',
    label: '计划月份',
  },
  {
    prop: 'rawOreProcessingVolume',
    label: '原矿处理量',
  },
  {
    prop: 'drySeparationVolume',
    label: '干选量',
  },
  {
    prop: 'grindingFeedVolume',
    label: '入磨量',
  },
  {
    prop: 'rawOreGrade',
    label: '原矿品位-TFe',
  },
  {
    prop: 'concentrateGrade',
    label: '精矿品位-TFe',
  },
  {
    prop: 'tailingGrade',
    label: '尾矿品位-TFe',
  },
  {
    prop: 'concentrateVolume',
    label: '精矿量',
  },
  {
    prop: 'todo',
    label: '操作',
    width: '200',
    fixed: !window.isSmallScreen ? 'right' : false,
    slotName: 'todo',
    showOverflowTooltip: false,
  },
]
export default (): BaseTableProps => {
  return {
    tableItem,
    elTableConfig: {
      tooltipOptions: {
        popperClass: 'lmw_popper',
        effect: 'light',
      },
      rowKey: 'id',
    },
    showIndex: false,
    showChoose: true,
    pagination: true,
  }
}
