import type { App } from 'vue'
import ExcelStepImport from './src/ExcelStepImport.vue'
import ValidationStep from './src/ValidationStep.vue'
import ExecuteStep from './src/ExecuteStep.vue'
import EditableCell from './src/EditableCell.vue'

// 组件列表
const components = [
  ExcelStepImport,
  ValidationStep,
  ExecuteStep,
  EditableCell
]

// 安装函数
const install = (app: App) => {
  components.forEach(component => {
    app.component(component.name || component.__name, component)
  })
}

// 导出安装函数和组件
export {
  install,
  ExcelStepImport,
  ValidationStep,
  ExecuteStep,
  EditableCell
}

// 默认导出安装函数
export default {
  install
}
