<template>
  <div class="w-full">
    <BaseEchart v-if="options" :options="options" height="600px" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseEchart from '@/BaseComponent/BaseEchart/src/echart.vue'
import { ElMessage } from 'element-plus'
import { apiDataStatsCrushing02b } from '@/apis/data'
import type { TDateCrushingOperationPeriodStats } from '@/apis/model'
import { ToolUtils } from '@/utils/tool'

interface Props {
  dateRange: {
    viewType: string
    startDate: string
    endDate: string
  }
}

const props = defineProps<Props>()

// 图表数据
const chartData = ref<TDateCrushingOperationPeriodStats[]>([])
const lastViewType = ref('monthly')

// 格式化日期显示
const formatDisplayDate = (item: any) => ToolUtils.formatDisplayDate(lastViewType.value, item)

const totalMinutes = computed(() => {
  return {
    daily: 24 * 60 / 3,
    weekly: 7 * 24 * 60 / 3,
    monthly: 30 * 24 * 60 / 3,
  }[props.dateRange.viewType] || 0
})

// 按日期和时段分组数据
const groupedData = computed(() => {
  const groups: Record<string, Record<string, { 
    operationTime?: number; 
    faultTime?: number;
    totalTime?: number;
  }>> = {}

  chartData.value.forEach((item: TDateCrushingOperationPeriodStats) => {
    const dateKey = formatDisplayDate(item)
    if (!dateKey) return

    if (!groups[dateKey]) {
      groups[dateKey] = {}
    }

    const period = item.workingPeriodName || '未知时段'
    // 每个时段8小时 = 480分钟
    const periodMinutes = 8 * 60
    
    groups[dateKey][period] = {
      operationTime: item.totalOperationTime || 0,
      faultTime: item.totalFaultTime || 0,
      totalTime: periodMinutes,
    }
  })

  return groups
})

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { viewType, startDate, endDate } = props.dateRange
    if (!startDate || !endDate) {
      return
    }

    const params = {
      viewType,
      startDate,
      endDate,
    }

    const res = await apiDataStatsCrushing02b(params)
    chartData.value = res || []
    lastViewType.value = viewType
  } catch (error) {
    console.error('获取运行时间数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 监听日期范围变化
watch(() => props.dateRange, () => {
  if (props.dateRange?.startDate) {
    fetchChartData()
  }
}, {
  immediate: true,
  deep: true
})

// 图表配置
const options = computed(() => {
  if (!chartData.value || !chartData.value.length) return undefined

  // 获取所有唯一的日期
  const dates = Object.keys(groupedData.value).sort()

  // 获取所有时段
  const periods = Array.from(
    new Set(
      Object.values(groupedData.value).flatMap((periods) =>
        Object.keys(periods)
      )
    )
  ).sort((a, b) => {
    const startA = Number(a.split('-')[0])
    const startB = Number(b.split('-')[0])
    return startA - startB
  })

  // 为每个时段创建三个堆叠系列：运行时间、故障时间、闲置时间
  const barSeries: any[] = []

  periods.forEach((period, periodIndex) => {
    // 运行时间
    barSeries.push({
      name: `${period}-运行时间`,
      type: 'bar',
      stack: period,
      barWidth: '20%',
      barGap: '10%',
      barCategoryGap: '40%',
      data: dates.map(date => {
        const data = groupedData.value[date]?.[period]
        return data?.operationTime || 0
      }),
      itemStyle: { color: '#4095E5' },
    })

    // 故障时间
    barSeries.push({
      name: `${period}-故障时间`,
      type: 'bar',
      stack: period,
      data: dates.map(date => {
        const data = groupedData.value[date]?.[period]
        return data?.faultTime || 0
      }),
      itemStyle: { color: `#E4C477` },
    })

    // 闲置时间
    barSeries.push({
      name: `${period}-闲置时间`,
      type: 'bar',
      stack: period,
      data: dates.map(date => {
        const data = groupedData.value[date]?.[period]
        if (!data) return 0
        const idleTime = Math.max(0, (totalMinutes.value || 0) - (data.operationTime || 0) - (data.faultTime || 0))
        return idleTime
      }),
      itemStyle: { color: `#81C2CB` },
    })
  })

  // 整体有效运行率折线图
  const lineSeries = [{
    name: '整体有效运行率',
    type: 'line',
    yAxisIndex: 1,
    symbol: 'circle',
    symbolSize: 6,
    itemStyle: { color: '#E74C3C' },
    lineStyle: { width: 2, color: '#E74C3C' },
    data: dates.map(date => {
      const periodData = groupedData.value[date]
      if (!periodData) return 0

      let totalOperationTime = 0
      let totalTime = 0

      Object.values(periodData).forEach(data => {
        totalOperationTime += data.operationTime || 0
        totalTime += totalMinutes.value || 0
      })

      return totalTime > 0 ? Number((totalOperationTime / totalTime * 100).toFixed(1)) : 0
    }),
  }]

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      formatter: (params: any[]) => {
        const date = params[0].axisValue
        let result = `${date}<br/>`

        // 按时段分组显示
        periods.forEach(period => {
          const data = groupedData.value[date]?.[period]
          if (data) {
            const idleTime = Math.max(0, (data.totalTime || 0) - (data.operationTime || 0) - (data.faultTime || 0))
            result += `<div style="margin: 5px 0;">
              <div style="font-weight: bold;">${period}</div>
              <div>运行时间: ${data.operationTime || 0}分钟</div>
              <div>故障时间: ${data.faultTime || 0}分钟</div>
              <div>闲置时间: ${idleTime}分钟</div>
            </div>`
          }
        })

        const efficiency = params.find(p => p.seriesName === '整体有效运行率')?.value || 0
        result += `<div style="font-weight: bold; color: #E74C3C;">整体有效运行率: ${efficiency}%</div>`

        return result
      }
    },
    legend: {
      data: [...periods.flatMap(p => [`${p}-运行时间`, `${p}-故障时间`, `${p}-闲置时间`]), '整体有效运行率'],
      bottom: 0,
      left: 'center',
      itemGap: 15,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 11,
        lineHeight: 14,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      top: '10%',
      bottom: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: ['5%', '5%'],
      data: dates,
      axisLabel: {
        rotate: 0,
        margin: 15,
        overflow: 'truncate',
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '时间(分钟)',
        min: 0,
        max: totalMinutes.value,
        interval: totalMinutes.value / 5,
        axisLabel: {
          formatter: '{value}',
        },
        axisLine: {
          lineStyle: {
            color: '#5B9BD5',
          },
        },
        nameTextStyle: {
          color: '#5B9BD5',
        },
      },
      {
        type: 'value',
        name: '有效运行率(%)',
        min: 0,
        max: 100,
        interval: 20,
        position: 'right',
        axisLabel: {
          formatter: '{value}%',
        },
        axisLine: {
          lineStyle: {
            color: '#E74C3C',
          },
        },
        nameTextStyle: {
          color: '#E74C3C',
        },
      }
    ],
    series: [...barSeries, ...lineSeries],
  }
})
</script>
