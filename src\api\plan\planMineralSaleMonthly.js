import request from '@/utils/request'

// 查询选矿销售月计划列表
export function listPlanMineralSaleMonthly(query) {
  return request({
    url: '/plan/planMineralSaleMonthly/list',
    method: 'get',
    params: query
  })
}

// 查询选矿销售月计划详细
export function getPlanMineralSaleMonthly(id) {
  return request({
    url: '/plan/planMineralSaleMonthly/' + id,
    method: 'get'
  })
}

// 新增选矿销售月计划
export function addPlanMineralSaleMonthly(data) {
  return request({
    url: '/plan/planMineralSaleMonthly',
    method: 'post',
    data: data
  })
}

// 修改选矿销售月计划
export function updatePlanMineralSaleMonthly(data) {
  return request({
    url: '/plan/planMineralSaleMonthly',
    method: 'put',
    data: data
  })
}

// 删除选矿销售月计划
export function delPlanMineralSaleMonthly(id) {
  return request({
    url: '/plan/planMineralSaleMonthly/' + id,
    method: 'delete'
  })
}
