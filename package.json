{"name": "lxbi-frontend", "private": true, "version": "4.0.0", "type": "module", "scripts": {"apis": "node bin/genApis.mjs", "dev": "vite", "build:prod": "vite build", "preview": "vite preview", "prettier": "prettier --write ."}, "dependencies": {"@codemirror/lang-json": "^6.0.2", "@codemirror/lint": "^6.8.5", "@codemirror/theme-one-dark": "^6.1.3", "@element-plus/icons-vue": "2.3.1", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.2.0", "async-wait-until": "^2.0.27", "axios": "^1.7.7", "codemirror": "^6.0.2", "codemirror-json5": "^1.0.3", "dayjs": "^1.11.10", "echarts": "^5.5.1", "element-plus": "^2.10.3", "exceljs": "^4.4.0", "file-saver": "2.0.5", "gridstack": "^12.2.2", "highlight.js": "^11.9.0", "jquery": "^3.7.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.1", "json5": "^2.2.3", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "node-fetch": "^3.3.2", "normalize.css": "^8.0.1", "nprogress": "0.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.1.3", "screenfull": "^6.0.2", "tony-mockjs": "^1.1.4", "unplugin-element-plus": "^0.10.0", "vue": "^3.5.15", "vue-codemirror": "^6.1.1", "vue-cropper": "1.0.3", "vue-draggable-plus": "^0.5.4", "vue-router": "^4.4.5"}, "devDependencies": {"@types/jquery": "^3.5.32", "@types/lodash-es": "^4.17.12", "@vitejs/plugin-vue": "^5.1.4", "prettier": "^3.0.3", "sass": "1.89.2", "typescript": "~5.6.2", "unocss": "^66.3.2", "unplugin-auto-import": "0.18.3", "unplugin-vue-components": "^0.27.4", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "^5.4.10", "vite-plugin-compression2": "^1.3.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.1.8"}, "packageManager": "pnpm@10.12.4"}