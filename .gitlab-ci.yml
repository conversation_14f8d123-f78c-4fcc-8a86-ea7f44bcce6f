stages:
  - package
  - image
package:
  image: node:22
  stage: package
  tags:
    - k3s
  only:
    refs:
      - develop
      - master
      - tags
  script:
    - corepack enable
    - pnpm --store-dir .pnpm-store install
    - pnpm build:prod
  cache:
    key: npm-ci-cache
    paths:
      - .pnpm-store
  artifacts:
    paths:
      - dist
    expire_in: 1h

branches:
  stage: image
  tags:
    - k3s
  only:
    refs:
      - master
      - develop
  image:
    name: moby/buildkit:rootless
    entrypoint: [ "sh", "-c" ]
  variables:
    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
  before_script:
    - |
      mkdir ~/.docker
      echo "{\"auths\":{\"***************:20080\":{\"auth\":\"************************\"}}}" > ~/.docker/config.json
  script:
    - |
      buildctl-daemonless.sh \
          build --frontend=dockerfile.v0 \
          --export-cache type=inline \
          --local context=. \
          --local dockerfile=. \
          --output type=image,name=***************:20080/congeer/lxbi-front:$CI_COMMIT_REF_NAME,push=true,registry.insecure=true

tags:
  stage: image
  tags:
    - k3s
  only:
    refs:
      - tags
  image:
    name: moby/buildkit:rootless
    entrypoint: [ "sh", "-c" ]
  variables:
    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
  before_script:
    - |
      mkdir ~/.docker
      echo "{\"auths\":{\"***************:20080\":{\"auth\":\"************************\"}}}" > ~/.docker/config.json
  script:
    - |
      buildctl-daemonless.sh \
          build --frontend=dockerfile.v0 \
          --export-cache type=inline \
          --local context=. \
          --local dockerfile=. \
          --output type=image,name=***************:20080/congeer/lxbi-front:$CI_COMMIT_TAG,push=true,registry.insecure=true
