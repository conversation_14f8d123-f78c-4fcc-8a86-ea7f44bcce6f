{"openapi": "3.1.0", "info": {"title": "标题：若依管理系统_接口文档", "description": "描述：用于管理集团旗下公司的人员信息,具体包括XXX,XXX模块...", "contact": {"name": "LXBI"}, "version": "版本号:0.1.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "security": [{"apikey": []}], "paths": {"/base/face": {"put": {"tags": ["sys-working-face-controller"], "operationId": "edit", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysWorkingFace"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}, "post": {"tags": ["sys-working-face-controller"], "operationId": "add", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysWorkingFace"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/actual/actual_hoist_data": {"put": {"tags": ["actual-hoist-data-controller"], "operationId": "edit_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActualHoistData"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}, "post": {"tags": ["actual-hoist-data-controller"], "operationId": "add_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActualHoistData"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/base/face/export": {"post": {"tags": ["sys-working-face-controller"], "operationId": "export", "parameters": [{"name": "sysWorkingFace", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysWorkingFace"}}], "responses": {"200": {"description": "OK"}}}}, "/actual/actual_hoist_data/export": {"post": {"tags": ["actual-hoist-data-controller"], "operationId": "export_1", "parameters": [{"name": "actualHoistData", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ActualHoistData"}}], "responses": {"200": {"description": "OK"}}}}, "/base/face/{workingFaceId}": {"get": {"tags": ["sys-working-face-controller"], "operationId": "getInfo", "parameters": [{"name": "workingFaceId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/base/face/list": {"get": {"tags": ["sys-working-face-controller"], "operationId": "list", "parameters": [{"name": "sysWorkingFace", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysWorkingFace"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/actual/hoist_stats/volume-period": {"get": {"tags": ["actual-hoist-stats-controller"], "operationId": "volumePeriod", "parameters": [{"name": "viewType", "in": "query", "required": false, "schema": {"type": "string", "default": "monthly"}}, {"name": "startDate", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "endDate", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/actual/hoist_stats/volume-all": {"get": {"tags": ["actual-hoist-stats-controller"], "operationId": "volumeAll", "parameters": [{"name": "viewType", "in": "query", "required": false, "schema": {"type": "string", "default": "monthly"}}, {"name": "startDate", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "endDate", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/actual/hoist_stats/time-all": {"get": {"tags": ["actual-hoist-stats-controller"], "operationId": "timeAll", "parameters": [{"name": "viewType", "in": "query", "required": false, "schema": {"type": "string", "default": "monthly"}}, {"name": "startDate", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "endDate", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/actual/hoist_stats/period-operation-time": {"get": {"tags": ["actual-hoist-stats-controller"], "operationId": "periodOperationTime", "parameters": [{"name": "viewType", "in": "query", "required": false, "schema": {"type": "string", "default": "all"}}, {"name": "startDate", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "endDate", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/actual/hoist_stats/fault-period": {"get": {"tags": ["actual-hoist-stats-controller"], "operationId": "dailyPeriodVolumeChart", "parameters": [{"name": "viewType", "in": "query", "required": false, "schema": {"type": "string", "default": "monthly"}}, {"name": "startDate", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "endDate", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/actual/hoist_stats/bucket-all": {"get": {"tags": ["actual-hoist-stats-controller"], "operationId": "bucketAll", "parameters": [{"name": "viewType", "in": "query", "required": false, "schema": {"type": "string", "default": "monthly"}}, {"name": "startDate", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "endDate", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/actual/actual_hoist_data/{hoistingDataId}": {"get": {"tags": ["actual-hoist-data-controller"], "operationId": "getInfo_1", "parameters": [{"name": "hoistingDataId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/actual/actual_hoist_data/list": {"get": {"tags": ["actual-hoist-data-controller"], "operationId": "list_1", "parameters": [{"name": "actualHoistData", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ActualHoistData"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TableDataInfo"}}}}}}}, "/base/face/{workingFaceIds}": {"delete": {"tags": ["sys-working-face-controller"], "operationId": "remove", "parameters": [{"name": "workingFaceIds", "in": "path", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/actual/actual_hoist_data/{hoistingDataIds}": {"delete": {"tags": ["actual-hoist-data-controller"], "operationId": "remove_1", "parameters": [{"name": "hoistingDataIds", "in": "path", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}}, "components": {"schemas": {"SysWorkingFace": {"type": "object", "properties": {"createBy": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "remark": {"type": "string"}, "params": {"type": "object", "additionalProperties": {}}, "workingFaceId": {"type": "integer", "format": "int64"}, "workingFaceName": {"type": "string"}, "status": {"type": "integer", "format": "int64"}, "userId": {"type": "string"}, "startTime": {"type": "string"}, "endTime": {"type": "string"}}}, "AjaxResult": {"type": "object", "additionalProperties": {}, "properties": {"error": {"type": "boolean"}, "success": {"type": "boolean"}, "warn": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "ActualHoistData": {"type": "object", "properties": {"createBy": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "remark": {"type": "string"}, "params": {"type": "object", "additionalProperties": {}}, "hoistingDataId": {"type": "integer", "format": "int64"}, "workingPeriodId": {"type": "integer", "format": "int64"}, "operationDate": {"type": "string", "format": "date-time"}, "operationTime": {"type": "integer", "format": "int64"}, "faultTime": {"type": "integer", "format": "int64"}, "buckets": {"type": "integer", "format": "int64"}, "weight": {"type": "string"}}}, "TableDataInfo": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "rows": {"type": "array", "items": {}}, "code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}}}, "securitySchemes": {"apikey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "scheme": "Bearer"}}}}