<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    :width="width"
    :closable="closable"
    :modal="modal"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="excel-step-import-dialog page"
  >
    <!-- 步骤指示器 -->
    <div class="step-indicator">
      <div
        v-for="(stepConfig, index) in stepConfigs"
        :key="stepConfig.step"
        :class="[
          'step',
          {
            active: stepConfig.active,
            completed: stepConfig.completed,
            clickable: stepConfig.clickable,
          },
        ]"
        @click="handleStepClick(stepConfig.step)"
      >
        <div class="step-number">
          <el-icon v-if="stepConfig.completed" class="step-icon">
            <Check />
          </el-icon>
          <span v-else>{{ stepConfig.step }}</span>
        </div>
        <span class="step-title">{{ stepConfig.title }}</span>
        <div v-if="index < stepConfigs.length - 1" class="step-connector"></div>
      </div>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 第一步：文件上传 -->
      <div v-show="currentStep === ImportStep.UPLOAD" class="step-upload">
        <div
          class="upload-area"
          :class="{ dragover: isDragOver }"
          @click="handleUploadClick"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @dragleave="handleDragLeave"
        >
          <div class="upload-icon">
            <el-icon size="48">
              <UploadFilled />
            </el-icon>
          </div>
          <div class="upload-text">拖拽Excel文件到此处</div>
          <div class="upload-hint">或点击选择文件</div>
          <input
            ref="fileInputRef"
            type="file"
            :accept="acceptTypesString"
            @change="handleFileSelect"
            style="display: none"
          />
        </div>

        <!-- 文件信息显示 -->
        <div v-if="fileInfo" class="file-info">
          <div class="file-details">
            <el-icon class="file-icon" size="32">
              <Document />
            </el-icon>
            <div class="file-meta">
              <div class="file-name">{{ fileInfo.name }}</div>
              <div class="file-size">大小: {{ fileInfo.sizeText }}</div>
            </div>
          </div>
          <el-button type="warning" @click="handleReUpload">重新上传</el-button>
        </div>

        <!-- 模板信息 -->
        <div class="template-info">
          <h4>文件格式要求：</h4>
          <ul>
            <li>支持 .xlsx 和 .xls 格式</li>
            <li>第一行为表头，从第二行开始为数据</li>
          </ul>

          <h4>必填字段：</h4>
          <ul v-if="templateInfo?.fields">
            <li v-for="field in requiredFields" :key="field.fieldName">
              {{ field.displayName }}
            </li>
          </ul>

          <div class="template-download">
            <el-button type="primary" link @click="handleDownloadTemplate">
              <el-icon><Download /></el-icon>
              下载导入模板
            </el-button>
          </div>
        </div>
      </div>

      <!-- 第二步：数据校验 -->
      <div v-show="currentStep === ImportStep.VALIDATE" class="step-validate">
        <ValidationStep
          v-if="validationResult"
          :validation-result="validationResult"
          :template-info="templateInfo"
          :file-info="fileInfo"
          @re-upload="goToStep(ImportStep.UPLOAD)"
          @cell-edit="handleCellEdit"
        />
      </div>

      <!-- 第三步：导入执行 -->
      <div v-show="currentStep === ImportStep.EXECUTE" class="step-execute">
        <ExecuteStep
          v-if="importResult"
          :import-result="importResult"
          :progress="progress"
          :status="status"
          @download-failures="handleDownloadFailures"
        />
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button
          v-if="currentStep === ImportStep.VALIDATE"
          @click="handlePrevious"
          :disabled="loading"
        >
          上一步
        </el-button>

        <el-button
          v-if="currentStep === ImportStep.UPLOAD"
          type="primary"
          @click="handleNext"
          :disabled="!fileInfo || loading"
          :loading="status === ImportStatus.VALIDATING"
        >
          下一步
        </el-button>

        <el-button
          v-if="currentStep === ImportStep.VALIDATE && canImport"
          type="primary"
          @click="handleConfirmImport"
          :disabled="!canImport || loading"
          :loading="status === ImportStatus.IMPORTING"
        >
          确认导入
        </el-button>

        <!-- 当有错误但有有效数据时，显示强制导入按钮 -->
        <el-button
          v-if="currentStep === ImportStep.VALIDATE && !canImport"
          type="warning"
          @click="handleForceImport"
          :disabled="loading"
          :loading="status === ImportStatus.IMPORTING"
        >
          强制导入
        </el-button>

        <el-button
          v-if="currentStep === ImportStep.EXECUTE"
          type="primary"
          @click="handleComplete"
          :disabled="status === ImportStatus.IMPORTING"
        >
          完成
        </el-button>

        <el-button
          v-if="currentStep !== ImportStep.EXECUTE"
          @click="handleCancel"
          :disabled="loading"
        >
          取消
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  downloadFailures,
  downloadTemplate,
  getTemplateInfo,
  importExcel,
  validateExcel,
} from '@/apis/excel-import'
import { TExcelImportResultObject, TExcelTemplateInfo } from '@/apis/model'
import {
  Check,
  Document,
  Download,
  UploadFilled,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, ref, watch } from 'vue'
import ExecuteStep from './ExecuteStep.vue'
import type {
  EditableCell,
  ExcelStepImportEmits,
  ExcelStepImportProps,
  FileInfo,
  StepConfig,
} from './types'
import {
  ImportStatus as ImportStatusEnum,
  ImportStep as ImportStepEnum,
} from './types'
import ValidationStep from './ValidationStep.vue'

// 定义组件属性
const props = withDefaults(defineProps<ExcelStepImportProps>(), {
  title: 'Excel数据导入',
  width: '1000px',
  closable: true,
  modal: true,
  maxFileSize: 10,
  acceptTypes: () => ['.xlsx', '.xls'],
  autoImport: false,
})

// 定义组件事件
const emit = defineEmits<ExcelStepImportEmits>()

// 引用
const fileInputRef = ref<HTMLInputElement>()

// 响应式状态
const currentStep = ref<ImportStepEnum>(ImportStepEnum.UPLOAD)
const status = ref<ImportStatusEnum>(ImportStatusEnum.IDLE)
const templateInfo = ref<TExcelTemplateInfo | null>(null)
const fileInfo = ref<FileInfo | null>(null)
const validationResult = ref<TExcelImportResultObject | null>(null)
const importResult = ref<TExcelImportResultObject | null>(null)
const progress = ref({
  current: 0,
  total: 0,
  percentage: 0,
  statusText: '',
})
const editingCell = ref<EditableCell | null>(null)
const loading = ref(false)
const errorMessage = ref('')
const isDragOver = ref(false)

// 计算属性
const ImportStep = ImportStepEnum
const ImportStatus = ImportStatusEnum

const acceptTypesString = computed(() => props.acceptTypes.join(','))

const requiredFields = computed(() => {
  return templateInfo.value?.fields?.filter((field) => field.required) || []
})

const stepConfigs = computed<StepConfig[]>(() => [
  {
    step: ImportStepEnum.UPLOAD,
    title: '上传文件',
    completed: currentStep.value > ImportStepEnum.UPLOAD,
    active: currentStep.value === ImportStepEnum.UPLOAD,
    clickable: true,
  },
  {
    step: ImportStepEnum.VALIDATE,
    title: '数据校验',
    completed: currentStep.value > ImportStepEnum.VALIDATE,
    active: currentStep.value === ImportStepEnum.VALIDATE,
    clickable: currentStep.value >= ImportStepEnum.VALIDATE,
  },
  {
    step: ImportStepEnum.EXECUTE,
    title: '导入执行',
    completed: status.value === ImportStatusEnum.SUCCESS,
    active: currentStep.value === ImportStepEnum.EXECUTE,
    clickable: currentStep.value >= ImportStepEnum.EXECUTE,
  },
])

const canImport = computed(() => {
  if (!validationResult.value || !validationResult.value.dataList) return false

  // 检查是否有错误类型的消息
  const hasErrorMessages = validationResult.value.dataList.some(
    (row) => row.messages && row.messages.some((msg) => msg.type === 'ERROR')
  )

  return !hasErrorMessages
})

const hasValidData = computed(() => {
  if (!validationResult.value || !validationResult.value.dataList) return false

  // 只要有至少一行没有错误就可以提交
  return validationResult.value.dataList.some(
    (row) => !row.messages || !row.messages.some((msg) => msg.type === 'ERROR')
  )
})

const errorRowCount = computed(() => {
  if (!validationResult.value || !validationResult.value.dataList) return 0

  return validationResult.value.dataList.filter(
    (row) => row.messages && row.messages.some((msg) => msg.type === 'ERROR')
  ).length
})

const validRowCount = computed(() => {
  if (!validationResult.value || !validationResult.value.dataList) return 0

  return validationResult.value.dataList.filter(
    (row) => !row.messages || !row.messages.some((msg) => msg.type === 'ERROR')
  ).length
})

// 监听器
watch(
  () => props.visible,
  async (visible) => {
    if (visible) {
      await initComponent()
    } else {
      resetComponent()
    }
  }
)

watch(
  () => props.templateKey,
  async (newKey) => {
    if (newKey && props.visible) {
      await loadTemplateInfo()
    }
  }
)

// 组件方法
const initComponent = async () => {
  resetComponent()
  await loadTemplateInfo()
}

const resetComponent = () => {
  currentStep.value = ImportStepEnum.UPLOAD
  status.value = ImportStatusEnum.IDLE
  fileInfo.value = null
  validationResult.value = null
  importResult.value = null
  progress.value = {
    current: 0,
    total: 0,
    percentage: 0,
    statusText: '',
  }
  editingCell.value = null
  loading.value = false
  errorMessage.value = ''
  isDragOver.value = false
}

const loadTemplateInfo = async () => {
  if (!props.templateKey) return

  try {
    loading.value = true
    const info = await getTemplateInfo(props.templateKey)
    templateInfo.value = info
  } catch (error) {
    console.error('加载模板信息失败:', error)
    ElMessage.error('加载模板信息失败')
    // 加载失败时关闭窗口
    emit('update:visible', false)
    emit('error', error)
  } finally {
    loading.value = false
  }
}

// 文件处理方法
const handleUploadClick = () => {
  fileInputRef.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    handleFile(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  const file = event.dataTransfer?.files[0]
  if (file) {
    handleFile(file)
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
}

const handleFile = (file: File) => {
  // 验证文件类型
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
  if (!props.acceptTypes.includes(fileExtension)) {
    ElMessage.error(`请选择${props.acceptTypes.join('或')}格式的文件`)
    return
  }

  // 验证文件大小
  const fileSizeMB = file.size / 1024 / 1024
  if (fileSizeMB > props.maxFileSize) {
    ElMessage.error(`文件大小不能超过${props.maxFileSize}MB`)
    return
  }

  // 创建文件信息
  fileInfo.value = {
    file,
    name: file.name,
    size: file.size,
    sizeText: formatFileSize(file.size),
    uploadTime: new Date(),
  }

  ElMessage.success('文件上传成功')
}

const handleReUpload = () => {
  fileInfo.value = null
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 从响应头中提取文件名
const getFileNameFromResponse = (response: any, defaultName: string): string => {
  try {
    // 尝试从 Content-Disposition 头中提取文件名
    const contentDisposition = response.headers['content-disposition'] || response.headers['Content-Disposition']
    if (contentDisposition) {
      // 匹配 filename*=UTF-8''filename 或 filename="filename" 格式
      const filenameMatch = contentDisposition.match(/filename\*=UTF-8''([^;]+)|filename="([^"]+)"|filename=([^;]+)/)
      if (filenameMatch) {
        const filename = filenameMatch[1] || filenameMatch[2] || filenameMatch[3]
        return decodeURIComponent(filename.trim())
      }
    }
  } catch (error) {
    console.warn('提取文件名失败:', error)
  }
  return defaultName
}

const handleDownloadTemplate = async () => {
  if (!props.templateKey) {
    ElMessage.error('模板key不能为空')
    return
  }

  try {
    loading.value = true
    const response = await downloadTemplate(props.templateKey)

    // 从响应头提取文件名
    const filename = getFileNameFromResponse(response, `${templateInfo.value?.name || '导入模板'}.xlsx`)

    // 创建下载链接
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  } finally {
    loading.value = false
  }
}

// 步骤导航方法
const goToStep = (step: ImportStepEnum) => {
  currentStep.value = step
}

const handleStepClick = (step: ImportStepEnum) => {
  const stepConfig = stepConfigs.value.find((s) => s.step === step)
  if (stepConfig?.clickable) {
    goToStep(step)
  }
}

const handleNext = async () => {
  if (currentStep.value === ImportStepEnum.UPLOAD) {
    await validateFile()
  }
}

const handlePrevious = () => {
  if (currentStep.value > ImportStepEnum.UPLOAD) {
    currentStep.value--
  }
}

const validateFile = async () => {
  if (!fileInfo.value) {
    ElMessage.error('请先选择文件')
    return
  }

  try {
    status.value = ImportStatusEnum.VALIDATING
    loading.value = true

    const formData = new FormData()
    formData.append('file', fileInfo.value.file)

    const result = await validateExcel(props.templateKey, formData)
    validationResult.value = result

    currentStep.value = ImportStepEnum.VALIDATE
    status.value = ImportStatusEnum.IDLE

    ElMessage.success('文件验证完成')
  } catch (error) {
    console.error('文件验证失败:', error)
    ElMessage.error('文件验证失败')
    status.value = ImportStatusEnum.ERROR
  } finally {
    loading.value = false
  }
}

const handleConfirmImport = async () => {
  if (!validationResult.value || !canImport.value) {
    ElMessage.error('存在验证错误，请修复后再导入')
    return
  }

  try {
    status.value = ImportStatusEnum.IMPORTING
    loading.value = true
    currentStep.value = ImportStepEnum.EXECUTE

    // 对于没有错误的情况，导入所有数据
    const dataList = validationResult.value.dataList || []

    // 模拟导入进度
    progress.value = {
      current: 0,
      total: dataList.length,
      percentage: 0,
      statusText: '正在导入数据...',
    }

    const result = await importExcel(
      props.templateKey,
      validationResult.value.id!,
      dataList
    )

    importResult.value = result
    status.value = ImportStatusEnum.SUCCESS
    progress.value.statusText = '导入完成'
    progress.value.percentage = 100

    ElMessage.success('数据导入成功')
    emit('success', result)
  } catch (error) {
    console.error('数据导入失败:', error)
    ElMessage.error('数据导入失败')
    status.value = ImportStatusEnum.ERROR
    emit('error', error)
  } finally {
    loading.value = false
  }
}

const handleForceImport = async () => {
  if (!validationResult.value) {
    ElMessage.error('没有有效数据可以导入')
    return
  }

  // 强制导入时直接显示确认对话框
  const errorCount = errorRowCount.value
  const validCount = validRowCount.value + validationResult.value.success!

  try {
    await ElMessageBox.confirm(
      `强制导入模式：将跳过 ${errorCount} 行错误数据，只导入 ${validCount} 行正常数据。确认继续吗？`,
      '强制导入确认',
      {
        confirmButtonText: '确认强制导入',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      }
    )
  } catch {
    // 用户取消
    return
  }

  // 执行导入逻辑（与handleConfirmImport相同的导入部分）
  try {
    status.value = ImportStatusEnum.IMPORTING
    loading.value = true
    currentStep.value = ImportStepEnum.EXECUTE

    // 过滤出没有错误的数据行
    const validDataList =
      validationResult.value.dataList?.filter(
        (row) =>
          !row.messages || !row.messages.some((msg) => msg.type === 'ERROR')
      ) || []

    // 模拟导入进度
    progress.value = {
      current: 0,
      total: validDataList.length,
      percentage: 0,
      statusText: '正在导入数据...',
    }

    const result = await importExcel(
      props.templateKey,
      validationResult.value.id!,
      validDataList
    )

    importResult.value = result
    status.value = ImportStatusEnum.SUCCESS
    progress.value.statusText = '导入完成'
    progress.value.percentage = 100

    ElMessage.success(
      `强制导入完成！成功导入 ${validCount} 条数据，跳过 ${errorCount} 条错误数据`
    )
    emit('success', result)
  } catch (error) {
    console.error('强制导入失败:', error)
    ElMessage.error('强制导入失败')
    status.value = ImportStatusEnum.ERROR
    emit('error', error)
  } finally {
    loading.value = false
  }
}

const handleComplete = () => {
  emit('update:visible', false)
  emit('close')
}

const handleCancel = async () => {
  if (loading.value) {
    const confirmed = await ElMessageBox.confirm(
      '正在处理中，确定要取消吗？',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '继续等待',
        type: 'warning',
      }
    ).catch(() => false)

    if (!confirmed) return
  }

  emit('update:visible', false)
  emit('cancel')
}

const handleClose = () => {
  if (!loading.value) {
    emit('update:visible', false)
    emit('close')
  }
}

// 数据编辑方法
const handleCellEdit = (cellData: EditableCell) => {
  editingCell.value = cellData
}

const handleDownloadFailures = async () => {
  if (!importResult.value?.id) {
    ElMessage.error('没有失败数据可下载')
    return
  }

  try {
    loading.value = true
    const response = await downloadFailures(
      props.templateKey,
      importResult.value.id
    )

    // 从响应头提取文件名
    const filename = getFileNameFromResponse(response, `导入失败数据_${new Date().toISOString().split('T')[0]}.xlsx`)

    // 创建下载链接
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('失败数据下载成功')
  } catch (error) {
    console.error('下载失败数据失败:', error)
    ElMessage.error('下载失败数据失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.excel-step-import-dialog {
  .step-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
    padding: 0 2rem;
  }

  .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #999;
    position: relative;
    flex: 1;
    max-width: 200px;

    &.active {
      color: var(--el-color-primary);
    }

    &.completed {
      color: var(--el-color-success);
    }

    &.clickable {
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }

  .step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    font-weight: bold;
    font-size: 14px;
    border: 2px solid transparent;

    .step-icon {
      color: white;
    }
  }

  .step.active .step-number {
    background: var(--el-color-primary);
    color: white;
    border-color: var(--el-color-primary);
  }

  .step.completed .step-number {
    background: var(--el-color-success);
    color: white;
    border-color: var(--el-color-success);
  }

  .step-title {
    font-size: 14px;
    font-weight: 500;
    text-align: center;
  }

  .step-connector {
    position: absolute;
    top: 16px;
    left: calc(50% + 16px);
    right: calc(-50% + 16px);
    height: 2px;
    background: #ddd;
    z-index: -1;
  }

  .step.completed + .step .step-connector {
    background: var(--el-color-success);
  }

  .step-content {
    min-height: 400px;
    padding: 1rem 0;
  }

  .step-upload {
    .upload-area {
      border: 2px dashed #ddd;
      border-radius: 8px;
      padding: 3rem;
      text-align: center;
      background: #fafafa;
      transition: all 0.3s;
      cursor: pointer;
      margin-bottom: 2rem;

      &:hover,
      &.dragover {
        border-color: var(--el-color-primary);
        background: #f0f4ff;
      }
    }

    .upload-icon {
      color: #ddd;
      margin-bottom: 1rem;

      .upload-area:hover &,
      .upload-area.dragover & {
        color: var(--el-color-primary);
      }
    }

    .upload-text {
      font-size: 1.1rem;
      color: #666;
      margin-bottom: 1rem;
    }

    .upload-hint {
      font-size: 0.9rem;
      color: #999;
    }

    .file-info {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .file-details {
      display: flex;
      align-items: center;
    }

    .file-icon {
      color: var(--el-color-success);
      margin-right: 1rem;
    }

    .file-meta {
      .file-name {
        font-weight: bold;
        margin-bottom: 0.25rem;
      }

      .file-size {
        color: #666;
        font-size: 0.9rem;
      }
    }

    .template-info {
      h4 {
        margin: 1rem 0 0.5rem 0;
        color: #495057;
      }

      ul {
        margin: 0 0 1rem 1.5rem;
        color: #666;

        li {
          margin-bottom: 0.25rem;
        }
      }

      .template-download {
        margin-top: 1rem;
        padding: 1rem;
        background: #e3f2fd;
        border-radius: 8px;
        text-align: center;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
  }
}
</style>
